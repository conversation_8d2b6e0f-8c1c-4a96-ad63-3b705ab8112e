// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: struct.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// 登录公共信息
type LoginCommon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Platform    string `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`       //平台
	Ver         string `protobuf:"bytes,2,opt,name=ver,proto3" json:"ver,omitempty"`                 //客户端当前版本
	DistanceId  string `protobuf:"bytes,3,opt,name=distanceId,proto3" json:"distanceId,omitempty"`   //ta访客id
	Os          string `protobuf:"bytes,4,opt,name=os,proto3" json:"os,omitempty"`                   //系统
	CloseGuide  bool   `protobuf:"varint,5,opt,name=closeGuide,proto3" json:"closeGuide,omitempty"`  //关闭新手引导
	InviteCode  string `protobuf:"bytes,6,opt,name=inviteCode,proto3" json:"inviteCode,omitempty"`   //邀请码
	PackageSign string `protobuf:"bytes,7,opt,name=packageSign,proto3" json:"packageSign,omitempty"` //包签名
}

func (x *LoginCommon) Reset() {
	*x = LoginCommon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginCommon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginCommon) ProtoMessage() {}

func (x *LoginCommon) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginCommon.ProtoReflect.Descriptor instead.
func (*LoginCommon) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{0}
}

func (x *LoginCommon) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *LoginCommon) GetVer() string {
	if x != nil {
		return x.Ver
	}
	return ""
}

func (x *LoginCommon) GetDistanceId() string {
	if x != nil {
		return x.DistanceId
	}
	return ""
}

func (x *LoginCommon) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *LoginCommon) GetCloseGuide() bool {
	if x != nil {
		return x.CloseGuide
	}
	return false
}

func (x *LoginCommon) GetInviteCode() string {
	if x != nil {
		return x.InviteCode
	}
	return ""
}

func (x *LoginCommon) GetPackageSign() string {
	if x != nil {
		return x.PackageSign
	}
	return ""
}

// 用户账号数据信息
type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid         string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`                  //用户全局唯一id
	LToken      string `protobuf:"bytes,2,opt,name=lToken,proto3" json:"lToken,omitempty"`            //登录服token，后续用来免密登录
	Age         int32  `protobuf:"varint,3,opt,name=age,proto3" json:"age,omitempty"`                 //年龄
	SignOutTime int32  `protobuf:"varint,4,opt,name=signOutTime,proto3" json:"signOutTime,omitempty"` //注销倒计时
	NickName    string `protobuf:"bytes,5,opt,name=nickName,proto3" json:"nickName,omitempty"`        //第三方用户名
	AvatarUrl   string `protobuf:"bytes,6,opt,name=avatarUrl,proto3" json:"avatarUrl,omitempty"`      //第三方头像
	Openid      string `protobuf:"bytes,7,opt,name=openid,proto3" json:"openid,omitempty"`            //第三方openid
	UserType    string `protobuf:"bytes,8,opt,name=userType,proto3" json:"userType,omitempty"`        //用户类型
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{1}
}

func (x *UserInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *UserInfo) GetLToken() string {
	if x != nil {
		return x.LToken
	}
	return ""
}

func (x *UserInfo) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *UserInfo) GetSignOutTime() int32 {
	if x != nil {
		return x.SignOutTime
	}
	return 0
}

func (x *UserInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *UserInfo) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *UserInfo) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *UserInfo) GetUserType() string {
	if x != nil {
		return x.UserType
	}
	return ""
}

// 物品基本数据
type ItemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid         string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`                  //物品uid
	Id          int32  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                   //物品配置
	Num         int32  `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`                 //物品数量
	SurplusTime int32  `protobuf:"varint,4,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"` //剩余时间
}

func (x *ItemInfo) Reset() {
	*x = ItemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemInfo) ProtoMessage() {}

func (x *ItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemInfo.ProtoReflect.Descriptor instead.
func (*ItemInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{2}
}

func (x *ItemInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ItemInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ItemInfo) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *ItemInfo) GetSurplusTime() int32 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

// 列车数据
type TrainInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Head         *CarriageInfo   `protobuf:"bytes,1,opt,name=head,proto3" json:"head,omitempty"`                  //车头
	Carriages    []*CarriageInfo `protobuf:"bytes,2,rep,name=carriages,proto3" json:"carriages,omitempty"`        //车厢
	ElectricTime int32           `protobuf:"varint,3,opt,name=electricTime,proto3" json:"electricTime,omitempty"` //电力加成剩余时间
	WaterTime    int32           `protobuf:"varint,4,opt,name=waterTime,proto3" json:"waterTime,omitempty"`       //水能加成剩余时间
}

func (x *TrainInfo) Reset() {
	*x = TrainInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainInfo) ProtoMessage() {}

func (x *TrainInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainInfo.ProtoReflect.Descriptor instead.
func (*TrainInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{3}
}

func (x *TrainInfo) GetHead() *CarriageInfo {
	if x != nil {
		return x.Head
	}
	return nil
}

func (x *TrainInfo) GetCarriages() []*CarriageInfo {
	if x != nil {
		return x.Carriages
	}
	return nil
}

func (x *TrainInfo) GetElectricTime() int32 {
	if x != nil {
		return x.ElectricTime
	}
	return 0
}

func (x *TrainInfo) GetWaterTime() int32 {
	if x != nil {
		return x.WaterTime
	}
	return 0
}

// 车厢产出数据
type CarriageOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val      int32 `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`           //当前累计
	AccTotal int64 `protobuf:"varint,2,opt,name=accTotal,proto3" json:"accTotal,omitempty"` //总累计
}

func (x *CarriageOutput) Reset() {
	*x = CarriageOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarriageOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarriageOutput) ProtoMessage() {}

func (x *CarriageOutput) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarriageOutput.ProtoReflect.Descriptor instead.
func (*CarriageOutput) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{4}
}

func (x *CarriageOutput) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

func (x *CarriageOutput) GetAccTotal() int64 {
	if x != nil {
		return x.AccTotal
	}
	return 0
}

// 车厢数据
type CarriageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int32                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                         //车厢id
	ThemeLv        int32                `protobuf:"varint,2,opt,name=themeLv,proto3" json:"themeLv,omitempty"`               //车厢主题等级
	Builds         []*TrainItemInfo     `protobuf:"bytes,3,rep,name=builds,proto3" json:"builds,omitempty"`                  //设施
	BuildTime      int32                `protobuf:"varint,4,opt,name=buildTime,proto3" json:"buildTime,omitempty"`           //建造剩余时间
	OpenDoor       bool                 `protobuf:"varint,5,opt,name=openDoor,proto3" json:"openDoor,omitempty"`             //是否打开猫猫门
	StarOutput     *CarriageOutput      `protobuf:"bytes,6,opt,name=starOutput,proto3" json:"starOutput,omitempty"`          //星尘产出
	HeartOutput    *CarriageOutput      `protobuf:"bytes,7,opt,name=heartOutput,proto3" json:"heartOutput,omitempty"`        //爱心产出
	ElectricOutput *CarriageOutput      `protobuf:"bytes,8,opt,name=electricOutput,proto3" json:"electricOutput,omitempty"`  //电力产出
	WaterOutput    *CarriageOutput      `protobuf:"bytes,9,opt,name=waterOutput,proto3" json:"waterOutput,omitempty"`        //水产出
	OutputTime     int32                `protobuf:"varint,10,opt,name=outputTime,proto3" json:"outputTime,omitempty"`        //产出累计时间
	Goods          []*CarriageGoodsInfo `protobuf:"bytes,11,rep,name=goods,proto3" json:"goods,omitempty"`                   //已解锁货物
	VitalityOutput *CarriageOutput      `protobuf:"bytes,12,opt,name=vitalityOutput,proto3" json:"vitalityOutput,omitempty"` //元气值产出
}

func (x *CarriageInfo) Reset() {
	*x = CarriageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarriageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarriageInfo) ProtoMessage() {}

func (x *CarriageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarriageInfo.ProtoReflect.Descriptor instead.
func (*CarriageInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{5}
}

func (x *CarriageInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CarriageInfo) GetThemeLv() int32 {
	if x != nil {
		return x.ThemeLv
	}
	return 0
}

func (x *CarriageInfo) GetBuilds() []*TrainItemInfo {
	if x != nil {
		return x.Builds
	}
	return nil
}

func (x *CarriageInfo) GetBuildTime() int32 {
	if x != nil {
		return x.BuildTime
	}
	return 0
}

func (x *CarriageInfo) GetOpenDoor() bool {
	if x != nil {
		return x.OpenDoor
	}
	return false
}

func (x *CarriageInfo) GetStarOutput() *CarriageOutput {
	if x != nil {
		return x.StarOutput
	}
	return nil
}

func (x *CarriageInfo) GetHeartOutput() *CarriageOutput {
	if x != nil {
		return x.HeartOutput
	}
	return nil
}

func (x *CarriageInfo) GetElectricOutput() *CarriageOutput {
	if x != nil {
		return x.ElectricOutput
	}
	return nil
}

func (x *CarriageInfo) GetWaterOutput() *CarriageOutput {
	if x != nil {
		return x.WaterOutput
	}
	return nil
}

func (x *CarriageInfo) GetOutputTime() int32 {
	if x != nil {
		return x.OutputTime
	}
	return 0
}

func (x *CarriageInfo) GetGoods() []*CarriageGoodsInfo {
	if x != nil {
		return x.Goods
	}
	return nil
}

func (x *CarriageInfo) GetVitalityOutput() *CarriageOutput {
	if x != nil {
		return x.VitalityOutput
	}
	return nil
}

// 设施数据
type TrainItemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Order int32 `protobuf:"varint,1,opt,name=order,proto3" json:"order,omitempty"` //设施序号
	Lv    int32 `protobuf:"varint,2,opt,name=lv,proto3" json:"lv,omitempty"`       //设施等级
	Skin  int32 `protobuf:"varint,3,opt,name=skin,proto3" json:"skin,omitempty"`   //设施皮肤
}

func (x *TrainItemInfo) Reset() {
	*x = TrainItemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainItemInfo) ProtoMessage() {}

func (x *TrainItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainItemInfo.ProtoReflect.Descriptor instead.
func (*TrainItemInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{6}
}

func (x *TrainItemInfo) GetOrder() int32 {
	if x != nil {
		return x.Order
	}
	return 0
}

func (x *TrainItemInfo) GetLv() int32 {
	if x != nil {
		return x.Lv
	}
	return 0
}

func (x *TrainItemInfo) GetSkin() int32 {
	if x != nil {
		return x.Skin
	}
	return 0
}

// 条件数据
type Condition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`         //条件id
	Num    int32  `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`       //条件数量
	Type   int32  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`     //条件类型
	IsHide bool   `protobuf:"varint,4,opt,name=isHide,proto3" json:"isHide,omitempty"` //是否隐藏
	Extra  string `protobuf:"bytes,5,opt,name=extra,proto3" json:"extra,omitempty"`    //扩展数据
}

func (x *Condition) Reset() {
	*x = Condition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Condition) ProtoMessage() {}

func (x *Condition) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Condition.ProtoReflect.Descriptor instead.
func (*Condition) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{7}
}

func (x *Condition) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Condition) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *Condition) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Condition) GetIsHide() bool {
	if x != nil {
		return x.IsHide
	}
	return false
}

func (x *Condition) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

// 产出数据
type Output struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`      //产出对象的id
	Items []*Condition `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"` //道具
}

func (x *Output) Reset() {
	*x = Output{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Output) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Output) ProtoMessage() {}

func (x *Output) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Output.ProtoReflect.Descriptor instead.
func (*Output) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{8}
}

func (x *Output) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Output) GetItems() []*Condition {
	if x != nil {
		return x.Items
	}
	return nil
}

// 加速能量数据
type Energy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Energy              int32 `protobuf:"varint,1,opt,name=energy,proto3" json:"energy,omitempty"`                           //加速能量
	CostRecoverNum      int32 `protobuf:"varint,2,opt,name=costRecoverNum,proto3" json:"costRecoverNum,omitempty"`           //收费恢复次数
	FreeRecoverNum      int32 `protobuf:"varint,3,opt,name=freeRecoverNum,proto3" json:"freeRecoverNum,omitempty"`           //免费恢复次数
	Used                bool  `protobuf:"varint,4,opt,name=used,proto3" json:"used,omitempty"`                               //使用过加速
	IsSpeedUp           bool  `protobuf:"varint,5,opt,name=isSpeedUp,proto3" json:"isSpeedUp,omitempty"`                     //是否正在加速
	IsUnlockSpeedUpAuto bool  `protobuf:"varint,6,opt,name=isUnlockSpeedUpAuto,proto3" json:"isUnlockSpeedUpAuto,omitempty"` //是否解锁了自动加速
}

func (x *Energy) Reset() {
	*x = Energy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Energy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Energy) ProtoMessage() {}

func (x *Energy) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Energy.ProtoReflect.Descriptor instead.
func (*Energy) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{9}
}

func (x *Energy) GetEnergy() int32 {
	if x != nil {
		return x.Energy
	}
	return 0
}

func (x *Energy) GetCostRecoverNum() int32 {
	if x != nil {
		return x.CostRecoverNum
	}
	return 0
}

func (x *Energy) GetFreeRecoverNum() int32 {
	if x != nil {
		return x.FreeRecoverNum
	}
	return 0
}

func (x *Energy) GetUsed() bool {
	if x != nil {
		return x.Used
	}
	return false
}

func (x *Energy) GetIsSpeedUp() bool {
	if x != nil {
		return x.IsSpeedUp
	}
	return false
}

func (x *Energy) GetIsUnlockSpeedUpAuto() bool {
	if x != nil {
		return x.IsUnlockSpeedUpAuto
	}
	return false
}

// 乘客数据
type PassengerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int32              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                                     //乘客配置id
	Level        int32              `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`                                                                                               //等级
	StarLv       int32              `protobuf:"varint,3,opt,name=starLv,proto3" json:"starLv,omitempty"`                                                                                             //星级
	Exp          int32              `protobuf:"varint,4,opt,name=exp,proto3" json:"exp,omitempty"`                                                                                                   //经验
	DormId       int32              `protobuf:"varint,5,opt,name=dormId,proto3" json:"dormId,omitempty"`                                                                                             //入住车厢
	HeartOutput  int32              `protobuf:"varint,6,opt,name=heartOutput,proto3" json:"heartOutput,omitempty"`                                                                                   //爱心产出
	StarOutput   int32              `protobuf:"varint,7,opt,name=starOutput,proto3" json:"starOutput,omitempty"`                                                                                     //星尘产出
	WorkId       int32              `protobuf:"varint,8,opt,name=workId,proto3" json:"workId,omitempty"`                                                                                             //工作车厢id
	WorkIndex    int32              `protobuf:"varint,9,opt,name=workIndex,proto3" json:"workIndex,omitempty"`                                                                                       //工位下标
	Plots        []*PassengerPlot   `protobuf:"bytes,10,rep,name=plots,proto3" json:"plots,omitempty"`                                                                                               //剧情
	UseSkinIndex int32              `protobuf:"varint,11,opt,name=useSkinIndex,proto3" json:"useSkinIndex,omitempty"`                                                                                //穿戴的皮肤
	DormIndex    int32              `protobuf:"varint,12,opt,name=dormIndex,proto3" json:"dormIndex,omitempty"`                                                                                      //坑位下标
	Talents      []*PassengerTalent `protobuf:"bytes,13,rep,name=talents,proto3" json:"talents,omitempty"`                                                                                           //天赋数据
	Profile      map[int32]int32    `protobuf:"bytes,14,rep,name=profile,proto3" json:"profile,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //资料数据
}

func (x *PassengerInfo) Reset() {
	*x = PassengerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassengerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassengerInfo) ProtoMessage() {}

func (x *PassengerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassengerInfo.ProtoReflect.Descriptor instead.
func (*PassengerInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{10}
}

func (x *PassengerInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PassengerInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PassengerInfo) GetStarLv() int32 {
	if x != nil {
		return x.StarLv
	}
	return 0
}

func (x *PassengerInfo) GetExp() int32 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *PassengerInfo) GetDormId() int32 {
	if x != nil {
		return x.DormId
	}
	return 0
}

func (x *PassengerInfo) GetHeartOutput() int32 {
	if x != nil {
		return x.HeartOutput
	}
	return 0
}

func (x *PassengerInfo) GetStarOutput() int32 {
	if x != nil {
		return x.StarOutput
	}
	return 0
}

func (x *PassengerInfo) GetWorkId() int32 {
	if x != nil {
		return x.WorkId
	}
	return 0
}

func (x *PassengerInfo) GetWorkIndex() int32 {
	if x != nil {
		return x.WorkIndex
	}
	return 0
}

func (x *PassengerInfo) GetPlots() []*PassengerPlot {
	if x != nil {
		return x.Plots
	}
	return nil
}

func (x *PassengerInfo) GetUseSkinIndex() int32 {
	if x != nil {
		return x.UseSkinIndex
	}
	return 0
}

func (x *PassengerInfo) GetDormIndex() int32 {
	if x != nil {
		return x.DormIndex
	}
	return 0
}

func (x *PassengerInfo) GetTalents() []*PassengerTalent {
	if x != nil {
		return x.Talents
	}
	return nil
}

func (x *PassengerInfo) GetProfile() map[int32]int32 {
	if x != nil {
		return x.Profile
	}
	return nil
}

// 乘客天赋数据
type PassengerTalent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`       //天赋id
	Level int32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"` //等级
}

func (x *PassengerTalent) Reset() {
	*x = PassengerTalent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassengerTalent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassengerTalent) ProtoMessage() {}

func (x *PassengerTalent) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassengerTalent.ProtoReflect.Descriptor instead.
func (*PassengerTalent) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{11}
}

func (x *PassengerTalent) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PassengerTalent) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

// 乘客剧情数据
type PassengerPlot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`      //剧情id
	Done bool   `protobuf:"varint,2,opt,name=done,proto3" json:"done,omitempty"` //是否已完成
}

func (x *PassengerPlot) Reset() {
	*x = PassengerPlot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassengerPlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassengerPlot) ProtoMessage() {}

func (x *PassengerPlot) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassengerPlot.ProtoReflect.Descriptor instead.
func (*PassengerPlot) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{12}
}

func (x *PassengerPlot) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PassengerPlot) GetDone() bool {
	if x != nil {
		return x.Done
	}
	return false
}

// 星球数据
type Planet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                       int32           `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                                            //星球id
	CurMapId                 int32           `protobuf:"varint,2,opt,name=curMapId,proto3" json:"curMapId,omitempty"`                                                                                                //当前地图id
	CurNodeId                int32           `protobuf:"varint,3,opt,name=curNodeId,proto3" json:"curNodeId,omitempty"`                                                                                              //当前节点id
	NodeProgress             float64         `protobuf:"fixed64,4,opt,name=nodeProgress,proto3" json:"nodeProgress,omitempty"`                                                                                       //当前节点进度
	NodePathProgress         int32           `protobuf:"varint,5,opt,name=nodePathProgress,proto3" json:"nodePathProgress,omitempty"`                                                                                //上个节点到当前节点进度
	Landed                   bool            `protobuf:"varint,6,opt,name=landed,proto3" json:"landed,omitempty"`                                                                                                    //是否登陆过星球
	Reached                  bool            `protobuf:"varint,7,opt,name=reached,proto3" json:"reached,omitempty"`                                                                                                  //是否到达过星球
	Branches                 []*PlanetBranch `protobuf:"bytes,8,rep,name=branches,proto3" json:"branches,omitempty"`                                                                                                 //星球支线
	ProfileData              map[int32]int32 `protobuf:"bytes,9,rep,name=profileData,proto3" json:"profileData,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //资料数据
	ProfileCollectReward     []int32         `protobuf:"varint,10,rep,packed,name=profileCollectReward,proto3" json:"profileCollectReward,omitempty"`                                                                //领取的星球资料奖励区域数据
	RoleNum                  int32           `protobuf:"varint,11,opt,name=roleNum,proto3" json:"roleNum,omitempty"`                                                                                                 //宣传玩法人口数
	PublicityUnGetOutputTime int32           `protobuf:"varint,12,opt,name=publicityUnGetOutputTime,proto3" json:"publicityUnGetOutputTime,omitempty"`                                                               //宣传玩法未收取时长累计
	PublicityOutputTime      int32           `protobuf:"varint,13,opt,name=publicityOutputTime,proto3" json:"publicityOutputTime,omitempty"`                                                                         //宣传玩法已累计时间
}

func (x *Planet) Reset() {
	*x = Planet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Planet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Planet) ProtoMessage() {}

func (x *Planet) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Planet.ProtoReflect.Descriptor instead.
func (*Planet) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{13}
}

func (x *Planet) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Planet) GetCurMapId() int32 {
	if x != nil {
		return x.CurMapId
	}
	return 0
}

func (x *Planet) GetCurNodeId() int32 {
	if x != nil {
		return x.CurNodeId
	}
	return 0
}

func (x *Planet) GetNodeProgress() float64 {
	if x != nil {
		return x.NodeProgress
	}
	return 0
}

func (x *Planet) GetNodePathProgress() int32 {
	if x != nil {
		return x.NodePathProgress
	}
	return 0
}

func (x *Planet) GetLanded() bool {
	if x != nil {
		return x.Landed
	}
	return false
}

func (x *Planet) GetReached() bool {
	if x != nil {
		return x.Reached
	}
	return false
}

func (x *Planet) GetBranches() []*PlanetBranch {
	if x != nil {
		return x.Branches
	}
	return nil
}

func (x *Planet) GetProfileData() map[int32]int32 {
	if x != nil {
		return x.ProfileData
	}
	return nil
}

func (x *Planet) GetProfileCollectReward() []int32 {
	if x != nil {
		return x.ProfileCollectReward
	}
	return nil
}

func (x *Planet) GetRoleNum() int32 {
	if x != nil {
		return x.RoleNum
	}
	return 0
}

func (x *Planet) GetPublicityUnGetOutputTime() int32 {
	if x != nil {
		return x.PublicityUnGetOutputTime
	}
	return 0
}

func (x *Planet) GetPublicityOutputTime() int32 {
	if x != nil {
		return x.PublicityOutputTime
	}
	return 0
}

// 探索数据
type PlanetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurPlanetId     int32     `protobuf:"varint,1,opt,name=curPlanetId,proto3" json:"curPlanetId,omitempty"`         //当前星球id
	MoveTargetId    int32     `protobuf:"varint,2,opt,name=moveTargetId,proto3" json:"moveTargetId,omitempty"`       //目标星球id
	MoveSurplusTime int32     `protobuf:"varint,3,opt,name=moveSurplusTime,proto3" json:"moveSurplusTime,omitempty"` //航行结束剩余时间>=0
	Planets         []*Planet `protobuf:"bytes,4,rep,name=planets,proto3" json:"planets,omitempty"`                  //星球数据
	RageMode        *RageMode `protobuf:"bytes,5,opt,name=rageMode,proto3" json:"rageMode,omitempty"`                //狂暴模式
}

func (x *PlanetInfo) Reset() {
	*x = PlanetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlanetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanetInfo) ProtoMessage() {}

func (x *PlanetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanetInfo.ProtoReflect.Descriptor instead.
func (*PlanetInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{14}
}

func (x *PlanetInfo) GetCurPlanetId() int32 {
	if x != nil {
		return x.CurPlanetId
	}
	return 0
}

func (x *PlanetInfo) GetMoveTargetId() int32 {
	if x != nil {
		return x.MoveTargetId
	}
	return 0
}

func (x *PlanetInfo) GetMoveSurplusTime() int32 {
	if x != nil {
		return x.MoveSurplusTime
	}
	return 0
}

func (x *PlanetInfo) GetPlanets() []*Planet {
	if x != nil {
		return x.Planets
	}
	return nil
}

func (x *PlanetInfo) GetRageMode() *RageMode {
	if x != nil {
		return x.RageMode
	}
	return nil
}

// 星球支线
type PlanetBranch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                           //支线id
	MapId       int32   `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`                    //地图id
	NodeId      int32   `protobuf:"varint,3,opt,name=nodeId,proto3" json:"nodeId,omitempty"`                  //当前节点id
	NodeRewards []int32 `protobuf:"varint,4,rep,packed,name=nodeRewards,proto3" json:"nodeRewards,omitempty"` //当前节点已领取奖励
}

func (x *PlanetBranch) Reset() {
	*x = PlanetBranch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlanetBranch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanetBranch) ProtoMessage() {}

func (x *PlanetBranch) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanetBranch.ProtoReflect.Descriptor instead.
func (*PlanetBranch) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{15}
}

func (x *PlanetBranch) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PlanetBranch) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *PlanetBranch) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *PlanetBranch) GetNodeRewards() []int32 {
	if x != nil {
		return x.NodeRewards
	}
	return nil
}

// 狂暴模式
type RageMode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`        //狂暴模式节点id
	Count int32  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"` //狂暴模式次数
}

func (x *RageMode) Reset() {
	*x = RageMode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RageMode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RageMode) ProtoMessage() {}

func (x *RageMode) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RageMode.ProtoReflect.Descriptor instead.
func (*RageMode) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{16}
}

func (x *RageMode) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RageMode) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 工具数据信息
type ToolInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"` //工具类型
	Lv   int32 `protobuf:"varint,2,opt,name=lv,proto3" json:"lv,omitempty"`     //工具等级
}

func (x *ToolInfo) Reset() {
	*x = ToolInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ToolInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToolInfo) ProtoMessage() {}

func (x *ToolInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToolInfo.ProtoReflect.Descriptor instead.
func (*ToolInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{17}
}

func (x *ToolInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ToolInfo) GetLv() int32 {
	if x != nil {
		return x.Lv
	}
	return 0
}

// 任务目标数据
type TaskTarget struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`    //当前目标id
	Num int32  `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"` //当前目标数量
}

func (x *TaskTarget) Reset() {
	*x = TaskTarget{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskTarget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskTarget) ProtoMessage() {}

func (x *TaskTarget) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskTarget.ProtoReflect.Descriptor instead.
func (*TaskTarget) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{18}
}

func (x *TaskTarget) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TaskTarget) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

// 任务数据
type Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`           //当前任务id
	Targets []*TaskTarget `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"` //任务目标进度
}

func (x *Task) Reset() {
	*x = Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{19}
}

func (x *Task) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Task) GetTargets() []*TaskTarget {
	if x != nil {
		return x.Targets
	}
	return nil
}

// 任务模块数据
type TaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tasks     []*Task  `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`         //当前任务
	Completes []string `protobuf:"bytes,2,rep,name=completes,proto3" json:"completes,omitempty"` //已完成的任务Id
}

func (x *TaskInfo) Reset() {
	*x = TaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskInfo) ProtoMessage() {}

func (x *TaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskInfo.ProtoReflect.Descriptor instead.
func (*TaskInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{20}
}

func (x *TaskInfo) GetTasks() []*Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *TaskInfo) GetCompletes() []string {
	if x != nil {
		return x.Completes
	}
	return nil
}

// 打造台数据
type ToolModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lv         int32               `protobuf:"varint,1,opt,name=Lv,proto3" json:"Lv,omitempty"`                                                                                               //当前等级
	Tools      map[int32]*ToolInfo `protobuf:"bytes,2,rep,name=tools,proto3" json:"tools,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //工具集合
	BlessCount int32               `protobuf:"varint,3,opt,name=blessCount,proto3" json:"blessCount,omitempty"`                                                                               //祝福次数
	BlessId    string              `protobuf:"bytes,4,opt,name=blessId,proto3" json:"blessId,omitempty"`                                                                                      //祝福id
}

func (x *ToolModel) Reset() {
	*x = ToolModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ToolModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToolModel) ProtoMessage() {}

func (x *ToolModel) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToolModel.ProtoReflect.Descriptor instead.
func (*ToolModel) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{21}
}

func (x *ToolModel) GetLv() int32 {
	if x != nil {
		return x.Lv
	}
	return 0
}

func (x *ToolModel) GetTools() map[int32]*ToolInfo {
	if x != nil {
		return x.Tools
	}
	return nil
}

func (x *ToolModel) GetBlessCount() int32 {
	if x != nil {
		return x.BlessCount
	}
	return 0
}

func (x *ToolModel) GetBlessId() string {
	if x != nil {
		return x.BlessId
	}
	return ""
}

// 基本的角色数据信息
type Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid                 string                       `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`                                                                                                      //角色id
	NickName            string                       `protobuf:"bytes,2,opt,name=nickName,proto3" json:"nickName,omitempty"`                                                                                            //角色名称
	AvatarUrl           string                       `protobuf:"bytes,3,opt,name=avatarUrl,proto3" json:"avatarUrl,omitempty"`                                                                                          //角色头像
	CreateTime          uint64                       `protobuf:"fixed64,4,opt,name=createTime,proto3" json:"createTime,omitempty"`                                                                                      //创建时间
	OfflineTime         uint64                       `protobuf:"fixed64,5,opt,name=offlineTime,proto3" json:"offlineTime,omitempty"`                                                                                    //离线时间
	TotalOnlineTime     uint64                       `protobuf:"fixed64,6,opt,name=totalOnlineTime,proto3" json:"totalOnlineTime,omitempty"`                                                                            //总在线时长
	Diamond             int32                        `protobuf:"varint,7,opt,name=diamond,proto3" json:"diamond,omitempty"`                                                                                             //钻石
	Heart               int32                        `protobuf:"varint,8,opt,name=heart,proto3" json:"heart,omitempty"`                                                                                                 //爱心
	StarDust            int32                        `protobuf:"varint,9,opt,name=starDust,proto3" json:"starDust,omitempty"`                                                                                           //星尘
	PaperCrane          int32                        `protobuf:"varint,10,opt,name=paperCrane,proto3" json:"paperCrane,omitempty"`                                                                                      //纸鹤
	Gm                  bool                         `protobuf:"varint,11,opt,name=gm,proto3" json:"gm,omitempty"`                                                                                                      //是不是gm
	Time                uint64                       `protobuf:"fixed64,12,opt,name=time,proto3" json:"time,omitempty"`                                                                                                 //游戏时长
	Energy              *Energy                      `protobuf:"bytes,13,opt,name=energy,proto3" json:"energy,omitempty"`                                                                                               //加速体力数据
	Bag                 []*ItemInfo                  `protobuf:"bytes,14,rep,name=bag,proto3" json:"bag,omitempty"`                                                                                                     //背包物品数据
	Train               *TrainInfo                   `protobuf:"bytes,15,opt,name=train,proto3" json:"train,omitempty"`                                                                                                 //列车数据
	Passengers          []*PassengerInfo             `protobuf:"bytes,16,rep,name=passengers,proto3" json:"passengers,omitempty"`                                                                                       //乘客数据
	PlanetInfo          *PlanetInfo                  `protobuf:"bytes,17,opt,name=planetInfo,proto3" json:"planetInfo,omitempty"`                                                                                       //探索数据
	GuideId             int32                        `protobuf:"varint,18,opt,name=guideId,proto3" json:"guideId,omitempty"`                                                                                            //当前引导模块id
	GuideInfo           []*GuideInfo                 `protobuf:"bytes,19,rep,name=guideInfo,proto3" json:"guideInfo,omitempty"`                                                                                         //引导数据
	TaskInfo            *TaskInfo                    `protobuf:"bytes,20,opt,name=taskInfo,proto3" json:"taskInfo,omitempty"`                                                                                           //任务数据
	ToolModel           *ToolModel                   `protobuf:"bytes,21,opt,name=toolModel,proto3" json:"toolModel,omitempty"`                                                                                         //打造台数据
	Explore             *Explore                     `protobuf:"bytes,22,opt,name=explore,proto3" json:"explore,omitempty"`                                                                                             //探索数据
	Wanted              *Wanted                      `protobuf:"bytes,23,opt,name=wanted,proto3" json:"wanted,omitempty"`                                                                                               //悬赏数据
	NextDaySurpluTime   int32                        `protobuf:"varint,24,opt,name=nextDaySurpluTime,proto3" json:"nextDaySurpluTime,omitempty"`                                                                        //下次每日刷新剩余时间
	MailList            []*MailInfo                  `protobuf:"bytes,25,rep,name=mailList,proto3" json:"mailList,omitempty"`                                                                                           //邮件列表(只有基本状态)
	AchievementInfo     *AchievementInfo             `protobuf:"bytes,26,opt,name=achievementInfo,proto3" json:"achievementInfo,omitempty"`                                                                             //成就任务数据
	ChangeNameCnt       int32                        `protobuf:"varint,27,opt,name=changeNameCnt,proto3" json:"changeNameCnt,omitempty"`                                                                                //改名次数
	NewMarkList         []*NewMarkInfo               `protobuf:"bytes,28,rep,name=newMarkList,proto3" json:"newMarkList,omitempty"`                                                                                     //new标签数据
	Chest               *Chest                       `protobuf:"bytes,29,opt,name=chest,proto3" json:"chest,omitempty"`                                                                                                 //宝箱模块
	HeartOutput         int32                        `protobuf:"varint,30,opt,name=heartOutput,proto3" json:"heartOutput,omitempty"`                                                                                    //爱心产出
	PassengerStarOutput int32                        `protobuf:"varint,31,opt,name=passengerStarOutput,proto3" json:"passengerStarOutput,omitempty"`                                                                    //乘客小费产出
	Tower               *Tower                       `protobuf:"bytes,32,opt,name=tower,proto3" json:"tower,omitempty"`                                                                                                 //爬塔模块
	BlackHole           *BlackHole                   `protobuf:"bytes,33,opt,name=blackHole,proto3" json:"blackHole,omitempty"`                                                                                         //黑洞模块
	Battle              *Battle                      `protobuf:"bytes,34,opt,name=battle,proto3" json:"battle,omitempty"`                                                                                               //战斗模块
	Resonance           *Resonance                   `protobuf:"bytes,35,opt,name=resonance,proto3" json:"resonance,omitempty"`                                                                                         //共鸣
	Equip               *Equip                       `protobuf:"bytes,36,opt,name=equip,proto3" json:"equip,omitempty"`                                                                                                 //装备模块
	Instance            *Instance                    `protobuf:"bytes,37,opt,name=instance,proto3" json:"instance,omitempty"`                                                                                           //副本模块
	PassengerRestCdTime int32                        `protobuf:"varint,38,opt,name=passengerRestCdTime,proto3" json:"passengerRestCdTime,omitempty"`                                                                    //乘客重置cd
	Store               *Store                       `protobuf:"bytes,39,opt,name=store,proto3" json:"store,omitempty"`                                                                                                 //商店数据
	Skin                map[int32]*PassengerSkinData `protobuf:"bytes,40,rep,name=skin,proto3" json:"skin,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`          //皮肤数据
	Jackpot             *Jackpot                     `protobuf:"bytes,41,opt,name=jackpot,proto3" json:"jackpot,omitempty"`                                                                                             //抽卡模块数据
	Pay                 *Pay                         `protobuf:"bytes,42,opt,name=pay,proto3" json:"pay,omitempty"`                                                                                                     //支付模块数据
	Transport           *Transport                   `protobuf:"bytes,43,opt,name=transport,proto3" json:"transport,omitempty"`                                                                                         //护送模块数据
	ConfigMd5           map[string]string            `protobuf:"bytes,44,rep,name=configMd5,proto3" json:"configMd5,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //服务器配置md5，仅仅dev下发
	Field               *Field                       `protobuf:"bytes,45,opt,name=field,proto3" json:"field,omitempty"`                                                                                                 //农场模块数据
	Frag                map[int32]int32              `protobuf:"bytes,46,rep,name=frag,proto3" json:"frag,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`         //投影数据
	Ore                 *Ore                         `protobuf:"bytes,47,opt,name=ore,proto3" json:"ore,omitempty"`                                                                                                     //矿场模块数据
	Collect             *Collect                     `protobuf:"bytes,48,opt,name=collect,proto3" json:"collect,omitempty"`                                                                                             //收集玩法模块数据
	Arrest              *ArrestModule                `protobuf:"bytes,49,opt,name=arrest,proto3" json:"arrest,omitempty"`                                                                                               //通缉令模块数据
	NextWeekSurplusTime int32                        `protobuf:"varint,50,opt,name=nextWeekSurplusTime,proto3" json:"nextWeekSurplusTime,omitempty"`                                                                    //下次周刷新时间
	SpaceStone          *SpaceStone                  `protobuf:"bytes,51,opt,name=spaceStone,proto3" json:"spaceStone,omitempty"`                                                                                       //空间石
	DailyTask           *DailyTaskInfo               `protobuf:"bytes,52,opt,name=dailyTask,proto3" json:"dailyTask,omitempty"`                                                                                         //每日任务
	PassengerProfiles   []int32                      `protobuf:"varint,53,rep,packed,name=passengerProfiles,proto3" json:"passengerProfiles,omitempty"`                                                                 //乘客资料
	PlanetProfiles      []int32                      `protobuf:"varint,54,rep,packed,name=planetProfiles,proto3" json:"planetProfiles,omitempty"`                                                                       //星球资料
	OfflineRewardTime   int32                        `protobuf:"varint,55,opt,name=offlineRewardTime,proto3" json:"offlineRewardTime,omitempty"`                                                                        //离线奖励时长
	OffsetTime          int32                        `protobuf:"varint,56,opt,name=offsetTime,proto3" json:"offsetTime,omitempty"`                                                                                      //偏移时间（debug环境使用）
	PvpModuleData       *PvpModuleData               `protobuf:"bytes,57,opt,name=pvpModuleData,proto3" json:"pvpModuleData,omitempty"`                                                                                 //pvp数据
	Ad                  *Ad                          `protobuf:"bytes,58,opt,name=ad,proto3" json:"ad,omitempty"`                                                                                                       //广告数据
	ProfileBranch       *ProfileBranch               `protobuf:"bytes,59,opt,name=profileBranch,proto3" json:"profileBranch,omitempty"`                                                                                 //记忆阁
	TrainDailyTask      *TrainDailyTask              `protobuf:"bytes,60,opt,name=trainDailyTask,proto3" json:"trainDailyTask,omitempty"`                                                                               //列车任务
	BurstTask           *BurstTask                   `protobuf:"bytes,61,opt,name=burstTask,proto3" json:"burstTask,omitempty"`                                                                                         //突发任务
	TrainActivity       *TrainActivity               `protobuf:"bytes,62,opt,name=trainActivity,proto3" json:"trainActivity,omitempty"`                                                                                 //列车活动
	TechData            *TechData                    `protobuf:"bytes,63,opt,name=techData,proto3" json:"techData,omitempty"`                                                                                           //科技数据
}

func (x *Player) Reset() {
	*x = Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Player) ProtoMessage() {}

func (x *Player) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Player.ProtoReflect.Descriptor instead.
func (*Player) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{22}
}

func (x *Player) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Player) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *Player) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *Player) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Player) GetOfflineTime() uint64 {
	if x != nil {
		return x.OfflineTime
	}
	return 0
}

func (x *Player) GetTotalOnlineTime() uint64 {
	if x != nil {
		return x.TotalOnlineTime
	}
	return 0
}

func (x *Player) GetDiamond() int32 {
	if x != nil {
		return x.Diamond
	}
	return 0
}

func (x *Player) GetHeart() int32 {
	if x != nil {
		return x.Heart
	}
	return 0
}

func (x *Player) GetStarDust() int32 {
	if x != nil {
		return x.StarDust
	}
	return 0
}

func (x *Player) GetPaperCrane() int32 {
	if x != nil {
		return x.PaperCrane
	}
	return 0
}

func (x *Player) GetGm() bool {
	if x != nil {
		return x.Gm
	}
	return false
}

func (x *Player) GetTime() uint64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *Player) GetEnergy() *Energy {
	if x != nil {
		return x.Energy
	}
	return nil
}

func (x *Player) GetBag() []*ItemInfo {
	if x != nil {
		return x.Bag
	}
	return nil
}

func (x *Player) GetTrain() *TrainInfo {
	if x != nil {
		return x.Train
	}
	return nil
}

func (x *Player) GetPassengers() []*PassengerInfo {
	if x != nil {
		return x.Passengers
	}
	return nil
}

func (x *Player) GetPlanetInfo() *PlanetInfo {
	if x != nil {
		return x.PlanetInfo
	}
	return nil
}

func (x *Player) GetGuideId() int32 {
	if x != nil {
		return x.GuideId
	}
	return 0
}

func (x *Player) GetGuideInfo() []*GuideInfo {
	if x != nil {
		return x.GuideInfo
	}
	return nil
}

func (x *Player) GetTaskInfo() *TaskInfo {
	if x != nil {
		return x.TaskInfo
	}
	return nil
}

func (x *Player) GetToolModel() *ToolModel {
	if x != nil {
		return x.ToolModel
	}
	return nil
}

func (x *Player) GetExplore() *Explore {
	if x != nil {
		return x.Explore
	}
	return nil
}

func (x *Player) GetWanted() *Wanted {
	if x != nil {
		return x.Wanted
	}
	return nil
}

func (x *Player) GetNextDaySurpluTime() int32 {
	if x != nil {
		return x.NextDaySurpluTime
	}
	return 0
}

func (x *Player) GetMailList() []*MailInfo {
	if x != nil {
		return x.MailList
	}
	return nil
}

func (x *Player) GetAchievementInfo() *AchievementInfo {
	if x != nil {
		return x.AchievementInfo
	}
	return nil
}

func (x *Player) GetChangeNameCnt() int32 {
	if x != nil {
		return x.ChangeNameCnt
	}
	return 0
}

func (x *Player) GetNewMarkList() []*NewMarkInfo {
	if x != nil {
		return x.NewMarkList
	}
	return nil
}

func (x *Player) GetChest() *Chest {
	if x != nil {
		return x.Chest
	}
	return nil
}

func (x *Player) GetHeartOutput() int32 {
	if x != nil {
		return x.HeartOutput
	}
	return 0
}

func (x *Player) GetPassengerStarOutput() int32 {
	if x != nil {
		return x.PassengerStarOutput
	}
	return 0
}

func (x *Player) GetTower() *Tower {
	if x != nil {
		return x.Tower
	}
	return nil
}

func (x *Player) GetBlackHole() *BlackHole {
	if x != nil {
		return x.BlackHole
	}
	return nil
}

func (x *Player) GetBattle() *Battle {
	if x != nil {
		return x.Battle
	}
	return nil
}

func (x *Player) GetResonance() *Resonance {
	if x != nil {
		return x.Resonance
	}
	return nil
}

func (x *Player) GetEquip() *Equip {
	if x != nil {
		return x.Equip
	}
	return nil
}

func (x *Player) GetInstance() *Instance {
	if x != nil {
		return x.Instance
	}
	return nil
}

func (x *Player) GetPassengerRestCdTime() int32 {
	if x != nil {
		return x.PassengerRestCdTime
	}
	return 0
}

func (x *Player) GetStore() *Store {
	if x != nil {
		return x.Store
	}
	return nil
}

func (x *Player) GetSkin() map[int32]*PassengerSkinData {
	if x != nil {
		return x.Skin
	}
	return nil
}

func (x *Player) GetJackpot() *Jackpot {
	if x != nil {
		return x.Jackpot
	}
	return nil
}

func (x *Player) GetPay() *Pay {
	if x != nil {
		return x.Pay
	}
	return nil
}

func (x *Player) GetTransport() *Transport {
	if x != nil {
		return x.Transport
	}
	return nil
}

func (x *Player) GetConfigMd5() map[string]string {
	if x != nil {
		return x.ConfigMd5
	}
	return nil
}

func (x *Player) GetField() *Field {
	if x != nil {
		return x.Field
	}
	return nil
}

func (x *Player) GetFrag() map[int32]int32 {
	if x != nil {
		return x.Frag
	}
	return nil
}

func (x *Player) GetOre() *Ore {
	if x != nil {
		return x.Ore
	}
	return nil
}

func (x *Player) GetCollect() *Collect {
	if x != nil {
		return x.Collect
	}
	return nil
}

func (x *Player) GetArrest() *ArrestModule {
	if x != nil {
		return x.Arrest
	}
	return nil
}

func (x *Player) GetNextWeekSurplusTime() int32 {
	if x != nil {
		return x.NextWeekSurplusTime
	}
	return 0
}

func (x *Player) GetSpaceStone() *SpaceStone {
	if x != nil {
		return x.SpaceStone
	}
	return nil
}

func (x *Player) GetDailyTask() *DailyTaskInfo {
	if x != nil {
		return x.DailyTask
	}
	return nil
}

func (x *Player) GetPassengerProfiles() []int32 {
	if x != nil {
		return x.PassengerProfiles
	}
	return nil
}

func (x *Player) GetPlanetProfiles() []int32 {
	if x != nil {
		return x.PlanetProfiles
	}
	return nil
}

func (x *Player) GetOfflineRewardTime() int32 {
	if x != nil {
		return x.OfflineRewardTime
	}
	return 0
}

func (x *Player) GetOffsetTime() int32 {
	if x != nil {
		return x.OffsetTime
	}
	return 0
}

func (x *Player) GetPvpModuleData() *PvpModuleData {
	if x != nil {
		return x.PvpModuleData
	}
	return nil
}

func (x *Player) GetAd() *Ad {
	if x != nil {
		return x.Ad
	}
	return nil
}

func (x *Player) GetProfileBranch() *ProfileBranch {
	if x != nil {
		return x.ProfileBranch
	}
	return nil
}

func (x *Player) GetTrainDailyTask() *TrainDailyTask {
	if x != nil {
		return x.TrainDailyTask
	}
	return nil
}

func (x *Player) GetBurstTask() *BurstTask {
	if x != nil {
		return x.BurstTask
	}
	return nil
}

func (x *Player) GetTrainActivity() *TrainActivity {
	if x != nil {
		return x.TrainActivity
	}
	return nil
}

func (x *Player) GetTechData() *TechData {
	if x != nil {
		return x.TechData
	}
	return nil
}

// 乘客对应皮肤信息组
type PassengerSkinData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*PassengerSkin `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"` //皮肤列表
}

func (x *PassengerSkinData) Reset() {
	*x = PassengerSkinData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassengerSkinData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassengerSkinData) ProtoMessage() {}

func (x *PassengerSkinData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassengerSkinData.ProtoReflect.Descriptor instead.
func (*PassengerSkinData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{23}
}

func (x *PassengerSkinData) GetList() []*PassengerSkin {
	if x != nil {
		return x.List
	}
	return nil
}

// 乘客对应皮肤信息
type PassengerSkin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` //皮肤序号
}

func (x *PassengerSkin) Reset() {
	*x = PassengerSkin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassengerSkin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassengerSkin) ProtoMessage() {}

func (x *PassengerSkin) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassengerSkin.ProtoReflect.Descriptor instead.
func (*PassengerSkin) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{24}
}

func (x *PassengerSkin) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

// 货币信息
type CurrencyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"` //货币类型 1钻石 2星尘 3爱心 4纸鹤
	Val  int32 `protobuf:"varint,2,opt,name=val,proto3" json:"val,omitempty"`   //货币值
}

func (x *CurrencyInfo) Reset() {
	*x = CurrencyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrencyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrencyInfo) ProtoMessage() {}

func (x *CurrencyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrencyInfo.ProtoReflect.Descriptor instead.
func (*CurrencyInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{25}
}

func (x *CurrencyInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CurrencyInfo) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

// 引导数据信息
type GuideInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                    //模块id
	KeySteps []int32 `protobuf:"varint,2,rep,packed,name=keySteps,proto3" json:"keySteps,omitempty"` //已经完成的关键步骤
}

func (x *GuideInfo) Reset() {
	*x = GuideInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GuideInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuideInfo) ProtoMessage() {}

func (x *GuideInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuideInfo.ProtoReflect.Descriptor instead.
func (*GuideInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{26}
}

func (x *GuideInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GuideInfo) GetKeySteps() []int32 {
	if x != nil {
		return x.KeySteps
	}
	return nil
}

// 邮件数据
type MailInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`           //id
	Time    uint64       `protobuf:"fixed64,2,opt,name=time,proto3" json:"time,omitempty"`     //创建时间
	Title   string       `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`     //邮件标题
	Content string       `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"` //邮件内容
	Rewards []*Condition `protobuf:"bytes,5,rep,name=rewards,proto3" json:"rewards,omitempty"` //附件
	Read    bool         `protobuf:"varint,6,opt,name=read,proto3" json:"read,omitempty"`      //是否读取
	Attach  bool         `protobuf:"varint,7,opt,name=attach,proto3" json:"attach,omitempty"`  //是否领取
}

func (x *MailInfo) Reset() {
	*x = MailInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MailInfo) ProtoMessage() {}

func (x *MailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MailInfo.ProtoReflect.Descriptor instead.
func (*MailInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{27}
}

func (x *MailInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MailInfo) GetTime() uint64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *MailInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *MailInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MailInfo) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *MailInfo) GetRead() bool {
	if x != nil {
		return x.Read
	}
	return false
}

func (x *MailInfo) GetAttach() bool {
	if x != nil {
		return x.Attach
	}
	return false
}

// 怪物数据
type Monster struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
	Lv int32 `protobuf:"varint,2,opt,name=lv,proto3" json:"lv,omitempty"` //等级
}

func (x *Monster) Reset() {
	*x = Monster{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Monster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Monster) ProtoMessage() {}

func (x *Monster) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Monster.ProtoReflect.Descriptor instead.
func (*Monster) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{28}
}

func (x *Monster) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Monster) GetLv() int32 {
	if x != nil {
		return x.Lv
	}
	return 0
}

// 悬赏模块数据
type Wanted struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*WantedInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"` //悬赏列表
}

func (x *Wanted) Reset() {
	*x = Wanted{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Wanted) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Wanted) ProtoMessage() {}

func (x *Wanted) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Wanted.ProtoReflect.Descriptor instead.
func (*Wanted) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{29}
}

func (x *Wanted) GetList() []*WantedInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 悬赏数据
type WantedInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        int32              `protobuf:"varint,1,opt,name=name,proto3" json:"name,omitempty"`               //名字下标
	Level       int32              `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`             //等级
	People      int32              `protobuf:"varint,3,opt,name=people,proto3" json:"people,omitempty"`           //需求人数
	SurplusTime int32              `protobuf:"varint,4,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"` //剩余时间
	State       int32              `protobuf:"varint,5,opt,name=state,proto3" json:"state,omitempty"`             //状态
	Rewards     []*Condition       `protobuf:"bytes,6,rep,name=rewards,proto3" json:"rewards,omitempty"`          //奖励
	Conditions  []*WantedCondition `protobuf:"bytes,7,rep,name=conditions,proto3" json:"conditions,omitempty"`    //条件
	Roles       []int32            `protobuf:"varint,8,rep,packed,name=roles,proto3" json:"roles,omitempty"`      //进行中的角色
	Publisher   int32              `protobuf:"varint,9,opt,name=publisher,proto3" json:"publisher,omitempty"`     //发布者
	Type        int32              `protobuf:"varint,10,opt,name=type,proto3" json:"type,omitempty"`              //类型
	Bg          int32              `protobuf:"varint,11,opt,name=bg,proto3" json:"bg,omitempty"`                  //主要事件
	BgArg       int32              `protobuf:"varint,12,opt,name=bgArg,proto3" json:"bgArg,omitempty"`            //事件参数
}

func (x *WantedInfo) Reset() {
	*x = WantedInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WantedInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WantedInfo) ProtoMessage() {}

func (x *WantedInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WantedInfo.ProtoReflect.Descriptor instead.
func (*WantedInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{30}
}

func (x *WantedInfo) GetName() int32 {
	if x != nil {
		return x.Name
	}
	return 0
}

func (x *WantedInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *WantedInfo) GetPeople() int32 {
	if x != nil {
		return x.People
	}
	return 0
}

func (x *WantedInfo) GetSurplusTime() int32 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

func (x *WantedInfo) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *WantedInfo) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *WantedInfo) GetConditions() []*WantedCondition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *WantedInfo) GetRoles() []int32 {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *WantedInfo) GetPublisher() int32 {
	if x != nil {
		return x.Publisher
	}
	return 0
}

func (x *WantedInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *WantedInfo) GetBg() int32 {
	if x != nil {
		return x.Bg
	}
	return 0
}

func (x *WantedInfo) GetBgArg() int32 {
	if x != nil {
		return x.BgArg
	}
	return 0
}

// 悬赏数据
type WantedCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`   //类型
	Value int32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"` //值
}

func (x *WantedCondition) Reset() {
	*x = WantedCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WantedCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WantedCondition) ProtoMessage() {}

func (x *WantedCondition) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WantedCondition.ProtoReflect.Descriptor instead.
func (*WantedCondition) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{31}
}

func (x *WantedCondition) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *WantedCondition) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

// 成就任务数据
type AchievementInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tasks        []*Task  `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`                        //当前成就任务
	Completes    []string `protobuf:"bytes,2,rep,name=completes,proto3" json:"completes,omitempty"`                //已完成的成就任务Id
	CompleteTime []uint64 `protobuf:"fixed64,3,rep,packed,name=completeTime,proto3" json:"completeTime,omitempty"` //完成任务时间
}

func (x *AchievementInfo) Reset() {
	*x = AchievementInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AchievementInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AchievementInfo) ProtoMessage() {}

func (x *AchievementInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AchievementInfo.ProtoReflect.Descriptor instead.
func (*AchievementInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{32}
}

func (x *AchievementInfo) GetTasks() []*Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *AchievementInfo) GetCompletes() []string {
	if x != nil {
		return x.Completes
	}
	return nil
}

func (x *AchievementInfo) GetCompleteTime() []uint64 {
	if x != nil {
		return x.CompleteTime
	}
	return nil
}

// new标签
type NewMarkInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TypeId int32   `protobuf:"varint,1,opt,name=typeId,proto3" json:"typeId,omitempty"`        //类型
	AryVal []int32 `protobuf:"varint,2,rep,packed,name=aryVal,proto3" json:"aryVal,omitempty"` //数据
}

func (x *NewMarkInfo) Reset() {
	*x = NewMarkInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewMarkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewMarkInfo) ProtoMessage() {}

func (x *NewMarkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewMarkInfo.ProtoReflect.Descriptor instead.
func (*NewMarkInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{33}
}

func (x *NewMarkInfo) GetTypeId() int32 {
	if x != nil {
		return x.TypeId
	}
	return 0
}

func (x *NewMarkInfo) GetAryVal() []int32 {
	if x != nil {
		return x.AryVal
	}
	return nil
}

// 仓库
type Storage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type int32 `protobuf:"varint,1,opt,name=Type,proto3" json:"Type,omitempty"` //类型
	Lv   int32 `protobuf:"varint,2,opt,name=Lv,proto3" json:"Lv,omitempty"`     //等级
}

func (x *Storage) Reset() {
	*x = Storage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Storage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Storage) ProtoMessage() {}

func (x *Storage) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Storage.ProtoReflect.Descriptor instead.
func (*Storage) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{34}
}

func (x *Storage) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Storage) GetLv() int32 {
	if x != nil {
		return x.Lv
	}
	return 0
}

// 宝箱
type BoxInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`   //宝箱id
	Num int32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"` //宝箱数量
}

func (x *BoxInfo) Reset() {
	*x = BoxInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoxInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoxInfo) ProtoMessage() {}

func (x *BoxInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoxInfo.ProtoReflect.Descriptor instead.
func (*BoxInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{35}
}

func (x *BoxInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BoxInfo) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

// 宝箱数据集
type BoxInfoArray struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*BoxInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"` //数据
}

func (x *BoxInfoArray) Reset() {
	*x = BoxInfoArray{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoxInfoArray) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoxInfoArray) ProtoMessage() {}

func (x *BoxInfoArray) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoxInfoArray.ProtoReflect.Descriptor instead.
func (*BoxInfoArray) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{36}
}

func (x *BoxInfoArray) GetData() []*BoxInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 宝箱模块
type Chest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Medal int32                   `protobuf:"varint,1,opt,name=medal,proto3" json:"medal,omitempty"`                                                                                       //总积分
	Step  int32                   `protobuf:"varint,2,opt,name=step,proto3" json:"step,omitempty"`                                                                                         //当前档位
	Data  map[int32]*BoxInfoArray `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //宝箱集合
}

func (x *Chest) Reset() {
	*x = Chest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Chest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chest) ProtoMessage() {}

func (x *Chest) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chest.ProtoReflect.Descriptor instead.
func (*Chest) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{37}
}

func (x *Chest) GetMedal() int32 {
	if x != nil {
		return x.Medal
	}
	return 0
}

func (x *Chest) GetStep() int32 {
	if x != nil {
		return x.Step
	}
	return 0
}

func (x *Chest) GetData() map[int32]*BoxInfoArray {
	if x != nil {
		return x.Data
	}
	return nil
}

// 爬塔
type Tower struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CheckPointId string `protobuf:"bytes,1,opt,name=checkPointId,proto3" json:"checkPointId,omitempty"` //当前关卡id
	IsDone       bool   `protobuf:"varint,2,opt,name=isDone,proto3" json:"isDone,omitempty"`            //已完成
}

func (x *Tower) Reset() {
	*x = Tower{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tower) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tower) ProtoMessage() {}

func (x *Tower) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tower.ProtoReflect.Descriptor instead.
func (*Tower) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{38}
}

func (x *Tower) GetCheckPointId() string {
	if x != nil {
		return x.CheckPointId
	}
	return ""
}

func (x *Tower) GetIsDone() bool {
	if x != nil {
		return x.IsDone
	}
	return false
}

// 黑洞
type BlackHole struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurId    string            `protobuf:"bytes,1,opt,name=curId,proto3" json:"curId,omitempty"`         //当前节点id
	NextId   string            `protobuf:"bytes,2,opt,name=nextId,proto3" json:"nextId,omitempty"`       //下个节点id
	Map      []*BlackHoleNode  `protobuf:"bytes,3,rep,name=map,proto3" json:"map,omitempty"`             //地图
	Buffs    []*BlackHoleBuff  `protobuf:"bytes,4,rep,name=buffs,proto3" json:"buffs,omitempty"`         //buff
	Roles    []*BattleRole     `protobuf:"bytes,5,rep,name=roles,proto3" json:"roles,omitempty"`         //选择的角色
	Aids     []*BattleRole     `protobuf:"bytes,6,rep,name=aids,proto3" json:"aids,omitempty"`           //援助
	Deads    []string          `protobuf:"bytes,7,rep,name=deads,proto3" json:"deads,omitempty"`         //已阵亡的角色
	Team     *BattleTeam       `protobuf:"bytes,8,opt,name=team,proto3" json:"team,omitempty"`           //编队
	Level    int32             `protobuf:"varint,9,opt,name=level,proto3" json:"level,omitempty"`        //当前难度
	Bosses   []*BlackHoleBoss  `protobuf:"bytes,10,rep,name=bosses,proto3" json:"bosses,omitempty"`      //不同难度的boss队伍
	Equips   []*BlackHoleEquip `protobuf:"bytes,11,rep,name=equips,proto3" json:"equips,omitempty"`      //科技装备
	Currency int32             `protobuf:"varint,12,opt,name=currency,proto3" json:"currency,omitempty"` //星海币
	IsUnlock bool              `protobuf:"varint,13,opt,name=isUnlock,proto3" json:"isUnlock,omitempty"` //是否解锁
	Add      float64           `protobuf:"fixed64,14,opt,name=add,proto3" json:"add,omitempty"`          //科技装备加成
}

func (x *BlackHole) Reset() {
	*x = BlackHole{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlackHole) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlackHole) ProtoMessage() {}

func (x *BlackHole) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlackHole.ProtoReflect.Descriptor instead.
func (*BlackHole) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{39}
}

func (x *BlackHole) GetCurId() string {
	if x != nil {
		return x.CurId
	}
	return ""
}

func (x *BlackHole) GetNextId() string {
	if x != nil {
		return x.NextId
	}
	return ""
}

func (x *BlackHole) GetMap() []*BlackHoleNode {
	if x != nil {
		return x.Map
	}
	return nil
}

func (x *BlackHole) GetBuffs() []*BlackHoleBuff {
	if x != nil {
		return x.Buffs
	}
	return nil
}

func (x *BlackHole) GetRoles() []*BattleRole {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *BlackHole) GetAids() []*BattleRole {
	if x != nil {
		return x.Aids
	}
	return nil
}

func (x *BlackHole) GetDeads() []string {
	if x != nil {
		return x.Deads
	}
	return nil
}

func (x *BlackHole) GetTeam() *BattleTeam {
	if x != nil {
		return x.Team
	}
	return nil
}

func (x *BlackHole) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *BlackHole) GetBosses() []*BlackHoleBoss {
	if x != nil {
		return x.Bosses
	}
	return nil
}

func (x *BlackHole) GetEquips() []*BlackHoleEquip {
	if x != nil {
		return x.Equips
	}
	return nil
}

func (x *BlackHole) GetCurrency() int32 {
	if x != nil {
		return x.Currency
	}
	return 0
}

func (x *BlackHole) GetIsUnlock() bool {
	if x != nil {
		return x.IsUnlock
	}
	return false
}

func (x *BlackHole) GetAdd() float64 {
	if x != nil {
		return x.Add
	}
	return 0
}

// 黑洞节点
type BlackHoleNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`           //节点id
	Type    int32             `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`      //节点类型
	Enemies []*BattleRole     `protobuf:"bytes,3,rep,name=enemies,proto3" json:"enemies,omitempty"` //敌人
	Buffs   []*BlackHoleBuff  `protobuf:"bytes,4,rep,name=buffs,proto3" json:"buffs,omitempty"`     //buff
	Aids    []*BattleRole     `protobuf:"bytes,5,rep,name=aids,proto3" json:"aids,omitempty"`       //援助
	IsFog   bool              `protobuf:"varint,6,opt,name=isFog,proto3" json:"isFog,omitempty"`    //是否为迷雾
	Reward  []*Condition      `protobuf:"bytes,7,rep,name=reward,proto3" json:"reward,omitempty"`   //奖励
	Equips  []*BlackHoleEquip `protobuf:"bytes,8,rep,name=equips,proto3" json:"equips,omitempty"`   //科技装备
}

func (x *BlackHoleNode) Reset() {
	*x = BlackHoleNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlackHoleNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlackHoleNode) ProtoMessage() {}

func (x *BlackHoleNode) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlackHoleNode.ProtoReflect.Descriptor instead.
func (*BlackHoleNode) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{40}
}

func (x *BlackHoleNode) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BlackHoleNode) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BlackHoleNode) GetEnemies() []*BattleRole {
	if x != nil {
		return x.Enemies
	}
	return nil
}

func (x *BlackHoleNode) GetBuffs() []*BlackHoleBuff {
	if x != nil {
		return x.Buffs
	}
	return nil
}

func (x *BlackHoleNode) GetAids() []*BattleRole {
	if x != nil {
		return x.Aids
	}
	return nil
}

func (x *BlackHoleNode) GetIsFog() bool {
	if x != nil {
		return x.IsFog
	}
	return false
}

func (x *BlackHoleNode) GetReward() []*Condition {
	if x != nil {
		return x.Reward
	}
	return nil
}

func (x *BlackHoleNode) GetEquips() []*BlackHoleEquip {
	if x != nil {
		return x.Equips
	}
	return nil
}

// 黑洞buff
type BlackHoleBuff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index   int32            `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`                                                                                     //buff下标
	Type    int32            `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`                                                                                       //buff类型
	Targets []string         `protobuf:"bytes,3,rep,name=targets,proto3" json:"targets,omitempty"`                                                                                  //作用uid
	Add     map[string]int32 `protobuf:"bytes,4,rep,name=add,proto3" json:"add,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //宝箱集合
}

func (x *BlackHoleBuff) Reset() {
	*x = BlackHoleBuff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlackHoleBuff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlackHoleBuff) ProtoMessage() {}

func (x *BlackHoleBuff) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlackHoleBuff.ProtoReflect.Descriptor instead.
func (*BlackHoleBuff) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{41}
}

func (x *BlackHoleBuff) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *BlackHoleBuff) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BlackHoleBuff) GetTargets() []string {
	if x != nil {
		return x.Targets
	}
	return nil
}

func (x *BlackHoleBuff) GetAdd() map[string]int32 {
	if x != nil {
		return x.Add
	}
	return nil
}

// 黑洞科技装备
type BlackHoleEquip struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`         //装备id
	Target int32 `protobuf:"varint,2,opt,name=target,proto3" json:"target,omitempty"` //作用目标
	Level  int32 `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`   //等级
}

func (x *BlackHoleEquip) Reset() {
	*x = BlackHoleEquip{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlackHoleEquip) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlackHoleEquip) ProtoMessage() {}

func (x *BlackHoleEquip) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlackHoleEquip.ProtoReflect.Descriptor instead.
func (*BlackHoleEquip) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{42}
}

func (x *BlackHoleEquip) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BlackHoleEquip) GetTarget() int32 {
	if x != nil {
		return x.Target
	}
	return 0
}

func (x *BlackHoleEquip) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

// 黑洞boss
type BlackHoleBoss struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level int32         `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"` //难度
	Roles []*BattleRole `protobuf:"bytes,2,rep,name=roles,proto3" json:"roles,omitempty"`  //角色
}

func (x *BlackHoleBoss) Reset() {
	*x = BlackHoleBoss{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlackHoleBoss) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlackHoleBoss) ProtoMessage() {}

func (x *BlackHoleBoss) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlackHoleBoss.ProtoReflect.Descriptor instead.
func (*BlackHoleBoss) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{43}
}

func (x *BlackHoleBoss) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *BlackHoleBoss) GetRoles() []*BattleRole {
	if x != nil {
		return x.Roles
	}
	return nil
}

// 战斗角色
type BattleRole struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid      string             `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`              //角色uid
	Id       int32              `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`               //角色id
	Type     int32              `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`           //乘客=1还是怪物=2
	Lv       int32              `protobuf:"varint,4,opt,name=lv,proto3" json:"lv,omitempty"`               //角色等级
	StarLv   int32              `protobuf:"varint,5,opt,name=starLv,proto3" json:"starLv,omitempty"`       //角色星级
	Attack   int32              `protobuf:"varint,6,opt,name=attack,proto3" json:"attack,omitempty"`       //角色攻击
	Hp       int32              `protobuf:"varint,7,opt,name=hp,proto3" json:"hp,omitempty"`               //角色血量
	Talents  []*PassengerTalent `protobuf:"bytes,8,rep,name=talents,proto3" json:"talents,omitempty"`      //天赋数据
	Equips   []*EquipItem       `protobuf:"bytes,9,rep,name=equips,proto3" json:"equips,omitempty"`        //装备
	AttrRate float64            `protobuf:"fixed64,10,opt,name=attrRate,proto3" json:"attrRate,omitempty"` //属性倍率
}

func (x *BattleRole) Reset() {
	*x = BattleRole{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleRole) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleRole) ProtoMessage() {}

func (x *BattleRole) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleRole.ProtoReflect.Descriptor instead.
func (*BattleRole) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{44}
}

func (x *BattleRole) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *BattleRole) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BattleRole) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BattleRole) GetLv() int32 {
	if x != nil {
		return x.Lv
	}
	return 0
}

func (x *BattleRole) GetStarLv() int32 {
	if x != nil {
		return x.StarLv
	}
	return 0
}

func (x *BattleRole) GetAttack() int32 {
	if x != nil {
		return x.Attack
	}
	return 0
}

func (x *BattleRole) GetHp() int32 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *BattleRole) GetTalents() []*PassengerTalent {
	if x != nil {
		return x.Talents
	}
	return nil
}

func (x *BattleRole) GetEquips() []*EquipItem {
	if x != nil {
		return x.Equips
	}
	return nil
}

func (x *BattleRole) GetAttrRate() float64 {
	if x != nil {
		return x.AttrRate
	}
	return 0
}

// 战斗角色
type BattleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsWin bool     `protobuf:"varint,1,opt,name=isWin,proto3" json:"isWin,omitempty"` //我方是否胜利
	Uids  []string `protobuf:"bytes,2,rep,name=uids,proto3" json:"uids,omitempty"`    //存活角色uid
}

func (x *BattleResult) Reset() {
	*x = BattleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleResult) ProtoMessage() {}

func (x *BattleResult) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleResult.ProtoReflect.Descriptor instead.
func (*BattleResult) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{45}
}

func (x *BattleResult) GetIsWin() bool {
	if x != nil {
		return x.IsWin
	}
	return false
}

func (x *BattleResult) GetUids() []string {
	if x != nil {
		return x.Uids
	}
	return nil
}

// 战斗编队
type BattleTeam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`    //编队id
	Uids []string `protobuf:"bytes,2,rep,name=uids,proto3" json:"uids,omitempty"` //角色
}

func (x *BattleTeam) Reset() {
	*x = BattleTeam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleTeam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleTeam) ProtoMessage() {}

func (x *BattleTeam) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleTeam.ProtoReflect.Descriptor instead.
func (*BattleTeam) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{46}
}

func (x *BattleTeam) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BattleTeam) GetUids() []string {
	if x != nil {
		return x.Uids
	}
	return nil
}

// 战斗编队
type Battle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Teams []*BattleTeam `protobuf:"bytes,1,rep,name=teams,proto3" json:"teams,omitempty"` //队伍
}

func (x *Battle) Reset() {
	*x = Battle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Battle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Battle) ProtoMessage() {}

func (x *Battle) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Battle.ProtoReflect.Descriptor instead.
func (*Battle) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{47}
}

func (x *Battle) GetTeams() []*BattleTeam {
	if x != nil {
		return x.Teams
	}
	return nil
}

// 装备信息
type Equip struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data        []*EquipItem     `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`                                                                                                        //装备列表
	Proficiency map[string]int32 `protobuf:"bytes,2,rep,name=proficiency,proto3" json:"proficiency,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //打造台熟练度
}

func (x *Equip) Reset() {
	*x = Equip{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Equip) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Equip) ProtoMessage() {}

func (x *Equip) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Equip.ProtoReflect.Descriptor instead.
func (*Equip) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{48}
}

func (x *Equip) GetData() []*EquipItem {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Equip) GetProficiency() map[string]int32 {
	if x != nil {
		return x.Proficiency
	}
	return nil
}

// 装备词条
type EquipEffect struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`       //id
	Attr  int32 `protobuf:"varint,2,opt,name=attr,proto3" json:"attr,omitempty"`   //属性
	Level int32 `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"` //等级
}

func (x *EquipEffect) Reset() {
	*x = EquipEffect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipEffect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipEffect) ProtoMessage() {}

func (x *EquipEffect) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipEffect.ProtoReflect.Descriptor instead.
func (*EquipEffect) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{49}
}

func (x *EquipEffect) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EquipEffect) GetAttr() int32 {
	if x != nil {
		return x.Attr
	}
	return 0
}

func (x *EquipEffect) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

// 装备信息
type EquipItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid     string         `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`         //唯一id
	Id      int32          `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`          //配置id
	Level   int32          `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`    //等级
	Effects []*EquipEffect `protobuf:"bytes,4,rep,name=effects,proto3" json:"effects,omitempty"` //词条
	Used    bool           `protobuf:"varint,5,opt,name=used,proto3" json:"used,omitempty"`      //是否穿戴中
}

func (x *EquipItem) Reset() {
	*x = EquipItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipItem) ProtoMessage() {}

func (x *EquipItem) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipItem.ProtoReflect.Descriptor instead.
func (*EquipItem) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{50}
}

func (x *EquipItem) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *EquipItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EquipItem) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *EquipItem) GetEffects() []*EquipEffect {
	if x != nil {
		return x.Effects
	}
	return nil
}

func (x *EquipItem) GetUsed() bool {
	if x != nil {
		return x.Used
	}
	return false
}

// 每日副本信息
type Instance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level            int32 `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`                       //最后一个通关的关卡
	IsUnlock         bool  `protobuf:"varint,2,opt,name=isUnlock,proto3" json:"isUnlock,omitempty"`                 //是否解锁
	IsCompletePuzzle bool  `protobuf:"varint,3,opt,name=isCompletePuzzle,proto3" json:"isCompletePuzzle,omitempty"` //是否完成解密
}

func (x *Instance) Reset() {
	*x = Instance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Instance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Instance) ProtoMessage() {}

func (x *Instance) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Instance.ProtoReflect.Descriptor instead.
func (*Instance) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{51}
}

func (x *Instance) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *Instance) GetIsUnlock() bool {
	if x != nil {
		return x.IsUnlock
	}
	return false
}

func (x *Instance) GetIsCompletePuzzle() bool {
	if x != nil {
		return x.IsCompletePuzzle
	}
	return false
}

// 商店模块数据
type Store struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*StoreInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"` //商店数据列表
}

func (x *Store) Reset() {
	*x = Store{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Store) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Store) ProtoMessage() {}

func (x *Store) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Store.ProtoReflect.Descriptor instead.
func (*Store) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{52}
}

func (x *Store) GetList() []*StoreInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 商店数据
type StoreInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                     //id
	RefreshCount int32    `protobuf:"varint,2,opt,name=refreshCount,proto3" json:"refreshCount,omitempty"` //已刷新次数
	Goods        []*Goods `protobuf:"bytes,3,rep,name=goods,proto3" json:"goods,omitempty"`                //商品
	RefreshTime  int32    `protobuf:"varint,4,opt,name=refreshTime,proto3" json:"refreshTime,omitempty"`   //下次刷新剩余时间
}

func (x *StoreInfo) Reset() {
	*x = StoreInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreInfo) ProtoMessage() {}

func (x *StoreInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreInfo.ProtoReflect.Descriptor instead.
func (*StoreInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{53}
}

func (x *StoreInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StoreInfo) GetRefreshCount() int32 {
	if x != nil {
		return x.RefreshCount
	}
	return 0
}

func (x *StoreInfo) GetGoods() []*Goods {
	if x != nil {
		return x.Goods
	}
	return nil
}

func (x *StoreInfo) GetRefreshTime() int32 {
	if x != nil {
		return x.RefreshTime
	}
	return 0
}

// 商品数据
type Goods struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Stock    int32      `protobuf:"varint,1,opt,name=stock,proto3" json:"stock,omitempty"`       //库存
	Item     *Condition `protobuf:"bytes,2,opt,name=item,proto3" json:"item,omitempty"`          //道具
	Discount int32      `protobuf:"varint,3,opt,name=discount,proto3" json:"discount,omitempty"` //折扣
	Cost     *Condition `protobuf:"bytes,4,opt,name=cost,proto3" json:"cost,omitempty"`          //消耗的货币，num取打折后的值
}

func (x *Goods) Reset() {
	*x = Goods{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Goods) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Goods) ProtoMessage() {}

func (x *Goods) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Goods.ProtoReflect.Descriptor instead.
func (*Goods) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{54}
}

func (x *Goods) GetStock() int32 {
	if x != nil {
		return x.Stock
	}
	return 0
}

func (x *Goods) GetItem() *Condition {
	if x != nil {
		return x.Item
	}
	return nil
}

func (x *Goods) GetDiscount() int32 {
	if x != nil {
		return x.Discount
	}
	return 0
}

func (x *Goods) GetCost() *Condition {
	if x != nil {
		return x.Cost
	}
	return nil
}

// 抽卡模块数据
type Jackpot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JackpotDailyNum   int32 `protobuf:"varint,1,opt,name=jackpotDailyNum,proto3" json:"jackpotDailyNum,omitempty"`     //当日抽卡次数
	JackpotTotalCount int32 `protobuf:"varint,2,opt,name=jackpotTotalCount,proto3" json:"jackpotTotalCount,omitempty"` //总累计抽卡次数
	JackpotPoints     int32 `protobuf:"varint,3,opt,name=jackpotPoints,proto3" json:"jackpotPoints,omitempty"`         //抽卡积分
}

func (x *Jackpot) Reset() {
	*x = Jackpot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Jackpot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Jackpot) ProtoMessage() {}

func (x *Jackpot) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Jackpot.ProtoReflect.Descriptor instead.
func (*Jackpot) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{55}
}

func (x *Jackpot) GetJackpotDailyNum() int32 {
	if x != nil {
		return x.JackpotDailyNum
	}
	return 0
}

func (x *Jackpot) GetJackpotTotalCount() int32 {
	if x != nil {
		return x.JackpotTotalCount
	}
	return 0
}

func (x *Jackpot) GetJackpotPoints() int32 {
	if x != nil {
		return x.JackpotPoints
	}
	return 0
}

// 支付数据
type Pay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NotFinishOrders []*NotFinishPayOrder `protobuf:"bytes,1,rep,name=notFinishOrders,proto3" json:"notFinishOrders,omitempty"`                                                                                  //未完成订单列表
	PayCountMap     map[string]int32     `protobuf:"bytes,2,rep,name=payCountMap,proto3" json:"payCountMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //部分商品的购买次数
}

func (x *Pay) Reset() {
	*x = Pay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pay) ProtoMessage() {}

func (x *Pay) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pay.ProtoReflect.Descriptor instead.
func (*Pay) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{56}
}

func (x *Pay) GetNotFinishOrders() []*NotFinishPayOrder {
	if x != nil {
		return x.NotFinishOrders
	}
	return nil
}

func (x *Pay) GetPayCountMap() map[string]int32 {
	if x != nil {
		return x.PayCountMap
	}
	return nil
}

// 未完成订单数据
type NotFinishPayOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CpOrderId string `protobuf:"bytes,1,opt,name=cpOrderId,proto3" json:"cpOrderId,omitempty"` //订单id
	ProductId string `protobuf:"bytes,2,opt,name=productId,proto3" json:"productId,omitempty"` //产品id
	Quantity  int32  `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`  //数量
	Platform  string `protobuf:"bytes,4,opt,name=platform,proto3" json:"platform,omitempty"`   //平台
}

func (x *NotFinishPayOrder) Reset() {
	*x = NotFinishPayOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotFinishPayOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotFinishPayOrder) ProtoMessage() {}

func (x *NotFinishPayOrder) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotFinishPayOrder.ProtoReflect.Descriptor instead.
func (*NotFinishPayOrder) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{57}
}

func (x *NotFinishPayOrder) GetCpOrderId() string {
	if x != nil {
		return x.CpOrderId
	}
	return ""
}

func (x *NotFinishPayOrder) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *NotFinishPayOrder) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *NotFinishPayOrder) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

// 餐厅菜单数据
type CarriageGoodsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`  //id
	Lv int32  `protobuf:"varint,2,opt,name=lv,proto3" json:"lv,omitempty"` //等级
}

func (x *CarriageGoodsInfo) Reset() {
	*x = CarriageGoodsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarriageGoodsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarriageGoodsInfo) ProtoMessage() {}

func (x *CarriageGoodsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarriageGoodsInfo.ProtoReflect.Descriptor instead.
func (*CarriageGoodsInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{58}
}

func (x *CarriageGoodsInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CarriageGoodsInfo) GetLv() int32 {
	if x != nil {
		return x.Lv
	}
	return 0
}

// 共鸣数据
type Resonance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Slots []*ResonanceSlot `protobuf:"bytes,1,rep,name=slots,proto3" json:"slots,omitempty"` //槽位
}

func (x *Resonance) Reset() {
	*x = Resonance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Resonance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resonance) ProtoMessage() {}

func (x *Resonance) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resonance.ProtoReflect.Descriptor instead.
func (*Resonance) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{59}
}

func (x *Resonance) GetSlots() []*ResonanceSlot {
	if x != nil {
		return x.Slots
	}
	return nil
}

// 共鸣槽数据
type ResonanceSlot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //乘客id
	Cd int32 `protobuf:"varint,2,opt,name=cd,proto3" json:"cd,omitempty"` //冷却时间
}

func (x *ResonanceSlot) Reset() {
	*x = ResonanceSlot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResonanceSlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResonanceSlot) ProtoMessage() {}

func (x *ResonanceSlot) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResonanceSlot.ProtoReflect.Descriptor instead.
func (*ResonanceSlot) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{60}
}

func (x *ResonanceSlot) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResonanceSlot) GetCd() int32 {
	if x != nil {
		return x.Cd
	}
	return 0
}

// 护送模块数据
type Transport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Exp  int32            `protobuf:"varint,1,opt,name=exp,proto3" json:"exp,omitempty"`  //当前经验
	List []*TransportData `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"` //
}

func (x *Transport) Reset() {
	*x = Transport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Transport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transport) ProtoMessage() {}

func (x *Transport) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transport.ProtoReflect.Descriptor instead.
func (*Transport) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{61}
}

func (x *Transport) GetExp() int32 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *Transport) GetList() []*TransportData {
	if x != nil {
		return x.List
	}
	return nil
}

// 单个护送数据
type TransportData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StarLv       int32                `protobuf:"varint,1,opt,name=starLv,proto3" json:"starLv,omitempty"`                             //星级
	Start        int32                `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`                               //起点星球
	End          int32                `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`                                   //终点星球
	Rewards      []*Condition         `protobuf:"bytes,4,rep,name=rewards,proto3" json:"rewards,omitempty"`                            //奖励数据
	FixRewards   []*Condition         `protobuf:"bytes,5,rep,name=fixRewards,proto3" json:"fixRewards,omitempty"`                      //额外必得奖励数据
	State        TransportDataState   `protobuf:"varint,6,opt,name=state,proto3,enum=proto.TransportDataState" json:"state,omitempty"` //数据状态
	Load         int32                `protobuf:"varint,7,opt,name=load,proto3" json:"load,omitempty"`                                 //任务负重要求
	Rare         bool                 `protobuf:"varint,8,opt,name=rare,proto3" json:"rare,omitempty"`                                 //是不是稀有任务
	TimeStoneKey bool                 `protobuf:"varint,9,opt,name=timeStoneKey,proto3" json:"timeStoneKey,omitempty"`                 //是不是时间之钥任务
	Actor        int32                `protobuf:"varint,10,opt,name=actor,proto3" json:"actor,omitempty"`                              //委托人
	Key          string               `protobuf:"bytes,11,opt,name=key,proto3" json:"key,omitempty"`                                   //委托对话
	BattleData   *TransportBattleData `protobuf:"bytes,12,opt,name=battleData,proto3" json:"battleData,omitempty"`                     //海盗数据
}

func (x *TransportData) Reset() {
	*x = TransportData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransportData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransportData) ProtoMessage() {}

func (x *TransportData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransportData.ProtoReflect.Descriptor instead.
func (*TransportData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{62}
}

func (x *TransportData) GetStarLv() int32 {
	if x != nil {
		return x.StarLv
	}
	return 0
}

func (x *TransportData) GetStart() int32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *TransportData) GetEnd() int32 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *TransportData) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *TransportData) GetFixRewards() []*Condition {
	if x != nil {
		return x.FixRewards
	}
	return nil
}

func (x *TransportData) GetState() TransportDataState {
	if x != nil {
		return x.State
	}
	return TransportDataState_NoneGet
}

func (x *TransportData) GetLoad() int32 {
	if x != nil {
		return x.Load
	}
	return 0
}

func (x *TransportData) GetRare() bool {
	if x != nil {
		return x.Rare
	}
	return false
}

func (x *TransportData) GetTimeStoneKey() bool {
	if x != nil {
		return x.TimeStoneKey
	}
	return false
}

func (x *TransportData) GetActor() int32 {
	if x != nil {
		return x.Actor
	}
	return 0
}

func (x *TransportData) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *TransportData) GetBattleData() *TransportBattleData {
	if x != nil {
		return x.BattleData
	}
	return nil
}

// 护送的战斗数据
type TransportBattleData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Monsters []*BattleRole `protobuf:"bytes,1,rep,name=monsters,proto3" json:"monsters,omitempty"` //敌人
	Second   int32         `protobuf:"varint,2,opt,name=second,proto3" json:"second,omitempty"`    //第多少s触发战斗
}

func (x *TransportBattleData) Reset() {
	*x = TransportBattleData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransportBattleData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransportBattleData) ProtoMessage() {}

func (x *TransportBattleData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransportBattleData.ProtoReflect.Descriptor instead.
func (*TransportBattleData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{63}
}

func (x *TransportBattleData) GetMonsters() []*BattleRole {
	if x != nil {
		return x.Monsters
	}
	return nil
}

func (x *TransportBattleData) GetSecond() int32 {
	if x != nil {
		return x.Second
	}
	return 0
}

// 农场数据
type Field struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CeilData  []*FieldCeil     `protobuf:"bytes,1,rep,name=ceilData,proto3" json:"ceilData,omitempty"`                                                                                            //格子数据列表
	SeedData  []*Condition     `protobuf:"bytes,2,rep,name=seedData,proto3" json:"seedData,omitempty"`                                                                                            //种子数据列表
	Level     int32            `protobuf:"varint,3,opt,name=Level,proto3" json:"Level,omitempty"`                                                                                                 //等级
	LevelCond map[string]int32 `protobuf:"bytes,4,rep,name=levelCond,proto3" json:"levelCond,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //升级条件数据
}

func (x *Field) Reset() {
	*x = Field{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Field) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Field) ProtoMessage() {}

func (x *Field) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Field.ProtoReflect.Descriptor instead.
func (*Field) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{64}
}

func (x *Field) GetCeilData() []*FieldCeil {
	if x != nil {
		return x.CeilData
	}
	return nil
}

func (x *Field) GetSeedData() []*Condition {
	if x != nil {
		return x.SeedData
	}
	return nil
}

func (x *Field) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *Field) GetLevelCond() map[string]int32 {
	if x != nil {
		return x.LevelCond
	}
	return nil
}

// 矿场数据
type Ore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecoverTime int32           `protobuf:"varint,1,opt,name=recoverTime,proto3" json:"recoverTime,omitempty"`                                                                                    //下一个镐子回复时间
	OreItems    map[int32]int32 `protobuf:"bytes,2,rep,name=oreItems,proto3" json:"oreItems,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //矿石数据
	Data        []*OreLevelData `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`                                                                                                   //矿洞数据
	IsUnlock    bool            `protobuf:"varint,4,opt,name=isUnlock,proto3" json:"isUnlock,omitempty"`                                                                                          //是否解锁
}

func (x *Ore) Reset() {
	*x = Ore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ore) ProtoMessage() {}

func (x *Ore) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ore.ProtoReflect.Descriptor instead.
func (*Ore) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{65}
}

func (x *Ore) GetRecoverTime() int32 {
	if x != nil {
		return x.RecoverTime
	}
	return 0
}

func (x *Ore) GetOreItems() map[int32]int32 {
	if x != nil {
		return x.OreItems
	}
	return nil
}

func (x *Ore) GetData() []*OreLevelData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Ore) GetIsUnlock() bool {
	if x != nil {
		return x.IsUnlock
	}
	return false
}

// 矿场难度对应的数据
type OreLevelData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level         int32         `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`                 //等级
	Depth         int32         `protobuf:"varint,2,opt,name=depth,proto3" json:"depth,omitempty"`                 //当前所在深度
	Data          []*OreRowData `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`                    //格子数据
	IsSpecialArea bool          `protobuf:"varint,4,opt,name=isSpecialArea,proto3" json:"isSpecialArea,omitempty"` //是否处于特殊区域层
}

func (x *OreLevelData) Reset() {
	*x = OreLevelData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OreLevelData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OreLevelData) ProtoMessage() {}

func (x *OreLevelData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OreLevelData.ProtoReflect.Descriptor instead.
func (*OreLevelData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{66}
}

func (x *OreLevelData) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *OreLevelData) GetDepth() int32 {
	if x != nil {
		return x.Depth
	}
	return 0
}

func (x *OreLevelData) GetData() []*OreRowData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *OreLevelData) GetIsSpecialArea() bool {
	if x != nil {
		return x.IsSpecialArea
	}
	return false
}

// 矿场单行数据
type OreRowData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*OreCeilData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"` //行数据
}

func (x *OreRowData) Reset() {
	*x = OreRowData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OreRowData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OreRowData) ProtoMessage() {}

func (x *OreRowData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OreRowData.ProtoReflect.Descriptor instead.
func (*OreRowData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{67}
}

func (x *OreRowData) GetData() []*OreCeilData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 矿场单格数据
type OreCeilData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type      OreCeilType                `protobuf:"varint,1,opt,name=type,proto3,enum=proto.OreCeilType" json:"type,omitempty"`                          //格子类型
	OreExtra  []*Condition               `protobuf:"bytes,2,rep,name=oreExtra,proto3" json:"oreExtra,omitempty"`                                          //如果是矿石格子,这个是矿石数据,怪物格子就是奖励数据
	Monsters  []*BattleRole              `protobuf:"bytes,3,rep,name=monsters,proto3" json:"monsters,omitempty"`                                          //如果是怪物格子,这个是战斗数据
	Ref       []*Point                   `protobuf:"bytes,4,rep,name=ref,proto3" json:"ref,omitempty"`                                                    //格子引用
	NextType  OrePageNextType            `protobuf:"varint,5,opt,name=nextType,proto3,enum=proto.OrePageNextType" json:"nextType,omitempty"`              //下一层类型
	Direction OreBlockItemDrillDirection `protobuf:"varint,6,opt,name=direction,proto3,enum=proto.OreBlockItemDrillDirection" json:"direction,omitempty"` //钻头方向
}

func (x *OreCeilData) Reset() {
	*x = OreCeilData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OreCeilData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OreCeilData) ProtoMessage() {}

func (x *OreCeilData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OreCeilData.ProtoReflect.Descriptor instead.
func (*OreCeilData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{68}
}

func (x *OreCeilData) GetType() OreCeilType {
	if x != nil {
		return x.Type
	}
	return OreCeilType_BlockNone
}

func (x *OreCeilData) GetOreExtra() []*Condition {
	if x != nil {
		return x.OreExtra
	}
	return nil
}

func (x *OreCeilData) GetMonsters() []*BattleRole {
	if x != nil {
		return x.Monsters
	}
	return nil
}

func (x *OreCeilData) GetRef() []*Point {
	if x != nil {
		return x.Ref
	}
	return nil
}

func (x *OreCeilData) GetNextType() OrePageNextType {
	if x != nil {
		return x.NextType
	}
	return OrePageNextType_NormalNext
}

func (x *OreCeilData) GetDirection() OreBlockItemDrillDirection {
	if x != nil {
		return x.Direction
	}
	return OreBlockItemDrillDirection_Left
}

// 农场格子数据
type FieldCeil struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                 //格子id
	Type        int32          `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`                             //格子类型
	SurplusTime int32          `protobuf:"varint,3,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"`               //生长剩余时间
	State       FieldCeilState `protobuf:"varint,4,opt,name=state,proto3,enum=proto.FieldCeilState" json:"state,omitempty"` //状态
	PlantId     int32          `protobuf:"varint,5,opt,name=plantId,proto3" json:"plantId,omitempty"`                       //作物id
}

func (x *FieldCeil) Reset() {
	*x = FieldCeil{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldCeil) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldCeil) ProtoMessage() {}

func (x *FieldCeil) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldCeil.ProtoReflect.Descriptor instead.
func (*FieldCeil) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{69}
}

func (x *FieldCeil) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FieldCeil) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *FieldCeil) GetSurplusTime() int32 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

func (x *FieldCeil) GetState() FieldCeilState {
	if x != nil {
		return x.State
	}
	return FieldCeilState_Empty
}

func (x *FieldCeil) GetPlantId() int32 {
	if x != nil {
		return x.PlantId
	}
	return 0
}

// 点位
type Point struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X int32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"` //x
	Y int32 `protobuf:"varint,2,opt,name=y,proto3" json:"y,omitempty"` //y
}

func (x *Point) Reset() {
	*x = Point{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Point) ProtoMessage() {}

func (x *Point) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Point.ProtoReflect.Descriptor instead.
func (*Point) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{70}
}

func (x *Point) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Point) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

// 采集玩法
type Collect struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mine []*MapMineItemData `protobuf:"bytes,1,rep,name=mine,proto3" json:"mine,omitempty"` //地图采集物数据
}

func (x *Collect) Reset() {
	*x = Collect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Collect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Collect) ProtoMessage() {}

func (x *Collect) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Collect.ProtoReflect.Descriptor instead.
func (*Collect) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{71}
}

func (x *Collect) GetMine() []*MapMineItemData {
	if x != nil {
		return x.Mine
	}
	return nil
}

// 每日任务模块数据
type DailyTaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tasks  []*DailyTask `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`    //任务列表
	BigGet bool         `protobuf:"varint,2,opt,name=bigGet,proto3" json:"bigGet,omitempty"` //是否领取大奖励
}

func (x *DailyTaskInfo) Reset() {
	*x = DailyTaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyTaskInfo) ProtoMessage() {}

func (x *DailyTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyTaskInfo.ProtoReflect.Descriptor instead.
func (*DailyTaskInfo) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{72}
}

func (x *DailyTaskInfo) GetTasks() []*DailyTask {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *DailyTaskInfo) GetBigGet() bool {
	if x != nil {
		return x.BigGet
	}
	return false
}

// 每日任务
type DailyTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid        string        `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`                //任务uid
	Id         int32         `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                 //任务id
	Target     []*Condition  `protobuf:"bytes,3,rep,name=target,proto3" json:"target,omitempty"`          //目标
	Progress   []*Condition  `protobuf:"bytes,4,rep,name=progress,proto3" json:"progress,omitempty"`      //进度
	Reward     []*Condition  `protobuf:"bytes,5,rep,name=reward,proto3" json:"reward,omitempty"`          //奖励
	State      int32         `protobuf:"varint,6,opt,name=state,proto3" json:"state,omitempty"`           //状态
	Sender     int32         `protobuf:"varint,7,opt,name=sender,proto3" json:"sender,omitempty"`         //任务发起者
	Content    int32         `protobuf:"varint,8,opt,name=content,proto3" json:"content,omitempty"`       //任务lang
	Planet     int32         `protobuf:"varint,9,opt,name=planet,proto3" json:"planet,omitempty"`         //战斗所在星球
	BattleInfo []*BattleRole `protobuf:"bytes,10,rep,name=battleInfo,proto3" json:"battleInfo,omitempty"` //战斗数据
}

func (x *DailyTask) Reset() {
	*x = DailyTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyTask) ProtoMessage() {}

func (x *DailyTask) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyTask.ProtoReflect.Descriptor instead.
func (*DailyTask) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{73}
}

func (x *DailyTask) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *DailyTask) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DailyTask) GetTarget() []*Condition {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *DailyTask) GetProgress() []*Condition {
	if x != nil {
		return x.Progress
	}
	return nil
}

func (x *DailyTask) GetReward() []*Condition {
	if x != nil {
		return x.Reward
	}
	return nil
}

func (x *DailyTask) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *DailyTask) GetSender() int32 {
	if x != nil {
		return x.Sender
	}
	return 0
}

func (x *DailyTask) GetContent() int32 {
	if x != nil {
		return x.Content
	}
	return 0
}

func (x *DailyTask) GetPlanet() int32 {
	if x != nil {
		return x.Planet
	}
	return 0
}

func (x *DailyTask) GetBattleInfo() []*BattleRole {
	if x != nil {
		return x.BattleInfo
	}
	return nil
}

// 地图采集物数据
type MapMineItemData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`            //采集物id
	Type     int32        `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`        //采集物类型
	Reward   []*Condition `protobuf:"bytes,3,rep,name=reward,proto3" json:"reward,omitempty"`     //奖励
	Position *Point       `protobuf:"bytes,4,opt,name=position,proto3" json:"position,omitempty"` //位置
	Uid      string       `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`           //唯一id
	Scale    float64      `protobuf:"fixed64,6,opt,name=scale,proto3" json:"scale,omitempty"`     //缩放比例
}

func (x *MapMineItemData) Reset() {
	*x = MapMineItemData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapMineItemData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapMineItemData) ProtoMessage() {}

func (x *MapMineItemData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapMineItemData.ProtoReflect.Descriptor instead.
func (*MapMineItemData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{74}
}

func (x *MapMineItemData) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MapMineItemData) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *MapMineItemData) GetReward() []*Condition {
	if x != nil {
		return x.Reward
	}
	return nil
}

func (x *MapMineItemData) GetPosition() *Point {
	if x != nil {
		return x.Position
	}
	return nil
}

func (x *MapMineItemData) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *MapMineItemData) GetScale() float64 {
	if x != nil {
		return x.Scale
	}
	return 0
}

// 时间宝石记录列表
type TimeStoneRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*TimeStoneRecordData `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"` //
}

func (x *TimeStoneRecord) Reset() {
	*x = TimeStoneRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeStoneRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeStoneRecord) ProtoMessage() {}

func (x *TimeStoneRecord) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeStoneRecord.ProtoReflect.Descriptor instead.
func (*TimeStoneRecord) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{75}
}

func (x *TimeStoneRecord) GetRecords() []*TimeStoneRecordData {
	if x != nil {
		return x.Records
	}
	return nil
}

// 时间宝石记录数据
type TimeStoneRecordData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`     //id
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"` //type
	Arg  string `protobuf:"bytes,3,opt,name=arg,proto3" json:"arg,omitempty"`   //参数
	Use  bool   `protobuf:"varint,4,opt,name=use,proto3" json:"use,omitempty"`  //是否回溯
}

func (x *TimeStoneRecordData) Reset() {
	*x = TimeStoneRecordData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeStoneRecordData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeStoneRecordData) ProtoMessage() {}

func (x *TimeStoneRecordData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeStoneRecordData.ProtoReflect.Descriptor instead.
func (*TimeStoneRecordData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{76}
}

func (x *TimeStoneRecordData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TimeStoneRecordData) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TimeStoneRecordData) GetArg() string {
	if x != nil {
		return x.Arg
	}
	return ""
}

func (x *TimeStoneRecordData) GetUse() bool {
	if x != nil {
		return x.Use
	}
	return false
}

// 通缉令列表数据
type ArrestModule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Arrests  []*Arrest     `protobuf:"bytes,1,rep,name=arrests,proto3" json:"arrests,omitempty"`    //列表
	Score    int32         `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`       //声望
	Result   *ArrestResult `protobuf:"bytes,3,opt,name=result,proto3" json:"result,omitempty"`      //上期战报
	Currency int32         `protobuf:"varint,4,opt,name=currency,proto3" json:"currency,omitempty"` //货币
}

func (x *ArrestModule) Reset() {
	*x = ArrestModule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArrestModule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArrestModule) ProtoMessage() {}

func (x *ArrestModule) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArrestModule.ProtoReflect.Descriptor instead.
func (*ArrestModule) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{77}
}

func (x *ArrestModule) GetArrests() []*Arrest {
	if x != nil {
		return x.Arrests
	}
	return nil
}

func (x *ArrestModule) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ArrestModule) GetResult() *ArrestResult {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *ArrestModule) GetCurrency() int32 {
	if x != nil {
		return x.Currency
	}
	return 0
}

// 通缉令数据
type Arrest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                               //id
	ExpirationTime int32         `protobuf:"varint,2,opt,name=expirationTime,proto3" json:"expirationTime,omitempty"`      //过期时间
	PlanetId       int32         `protobuf:"varint,3,opt,name=planetId,proto3" json:"planetId,omitempty"`                  //星球id
	Star           int32         `protobuf:"varint,4,opt,name=star,proto3" json:"star,omitempty"`                          //星级
	State          ArrestState   `protobuf:"varint,5,opt,name=state,proto3,enum=proto.ArrestState" json:"state,omitempty"` //状态
	Monsters       []*BattleRole `protobuf:"bytes,6,rep,name=monsters,proto3" json:"monsters,omitempty"`                   //怪物数据
	Rewards        []*Condition  `protobuf:"bytes,7,rep,name=rewards,proto3" json:"rewards,omitempty"`                     //奖励
	StoryId        int32         `protobuf:"varint,8,opt,name=storyId,proto3" json:"storyId,omitempty"`                    //罪名id
	Clues          []*ArrestClue `protobuf:"bytes,9,rep,name=clues,proto3" json:"clues,omitempty"`                         //线索
}

func (x *Arrest) Reset() {
	*x = Arrest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Arrest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Arrest) ProtoMessage() {}

func (x *Arrest) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Arrest.ProtoReflect.Descriptor instead.
func (*Arrest) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{78}
}

func (x *Arrest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Arrest) GetExpirationTime() int32 {
	if x != nil {
		return x.ExpirationTime
	}
	return 0
}

func (x *Arrest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *Arrest) GetStar() int32 {
	if x != nil {
		return x.Star
	}
	return 0
}

func (x *Arrest) GetState() ArrestState {
	if x != nil {
		return x.State
	}
	return ArrestState_NotCollected
}

func (x *Arrest) GetMonsters() []*BattleRole {
	if x != nil {
		return x.Monsters
	}
	return nil
}

func (x *Arrest) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *Arrest) GetStoryId() int32 {
	if x != nil {
		return x.StoryId
	}
	return 0
}

func (x *Arrest) GetClues() []*ArrestClue {
	if x != nil {
		return x.Clues
	}
	return nil
}

// 通缉令线索
type ArrestClue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       ArrestClueType  `protobuf:"varint,1,opt,name=type,proto3,enum=proto.ArrestClueType" json:"type,omitempty"`            //类型
	Planets    []int32         `protobuf:"varint,2,rep,packed,name=planets,proto3" json:"planets,omitempty"`                         //星球id
	IsHideInfo bool            `protobuf:"varint,3,opt,name=isHideInfo,proto3" json:"isHideInfo,omitempty"`                          //是否隐藏信息
	PlaceType  ArrestPlaceType `protobuf:"varint,4,opt,name=placeType,proto3,enum=proto.ArrestPlaceType" json:"placeType,omitempty"` //地点类型
	TimeType   ArrestTimeType  `protobuf:"varint,5,opt,name=timeType,proto3,enum=proto.ArrestTimeType" json:"timeType,omitempty"`    //时间类型
}

func (x *ArrestClue) Reset() {
	*x = ArrestClue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArrestClue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArrestClue) ProtoMessage() {}

func (x *ArrestClue) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArrestClue.ProtoReflect.Descriptor instead.
func (*ArrestClue) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{79}
}

func (x *ArrestClue) GetType() ArrestClueType {
	if x != nil {
		return x.Type
	}
	return ArrestClueType_PLACE
}

func (x *ArrestClue) GetPlanets() []int32 {
	if x != nil {
		return x.Planets
	}
	return nil
}

func (x *ArrestClue) GetIsHideInfo() bool {
	if x != nil {
		return x.IsHideInfo
	}
	return false
}

func (x *ArrestClue) GetPlaceType() ArrestPlaceType {
	if x != nil {
		return x.PlaceType
	}
	return ArrestPlaceType_NONE
}

func (x *ArrestClue) GetTimeType() ArrestTimeType {
	if x != nil {
		return x.TimeType
	}
	return ArrestTimeType_ALL
}

// 通缉战报
type ArrestResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wins  []*ArrestResultDetail `protobuf:"bytes,1,rep,name=wins,proto3" json:"wins,omitempty"`    //已完成
	Fails []*ArrestResultDetail `protobuf:"bytes,2,rep,name=fails,proto3" json:"fails,omitempty"`  //未完成
	Score int32                 `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"` //声望变化
}

func (x *ArrestResult) Reset() {
	*x = ArrestResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArrestResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArrestResult) ProtoMessage() {}

func (x *ArrestResult) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArrestResult.ProtoReflect.Descriptor instead.
func (*ArrestResult) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{80}
}

func (x *ArrestResult) GetWins() []*ArrestResultDetail {
	if x != nil {
		return x.Wins
	}
	return nil
}

func (x *ArrestResult) GetFails() []*ArrestResultDetail {
	if x != nil {
		return x.Fails
	}
	return nil
}

func (x *ArrestResult) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

// 通缉战报详情
type ArrestResultDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Star  int32 `protobuf:"varint,1,opt,name=star,proto3" json:"star,omitempty"`   //星级
	Count int32 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"` //数量
}

func (x *ArrestResultDetail) Reset() {
	*x = ArrestResultDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArrestResultDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArrestResultDetail) ProtoMessage() {}

func (x *ArrestResultDetail) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArrestResultDetail.ProtoReflect.Descriptor instead.
func (*ArrestResultDetail) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{81}
}

func (x *ArrestResultDetail) GetStar() int32 {
	if x != nil {
		return x.Star
	}
	return 0
}

func (x *ArrestResultDetail) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 空间宝石
type SpaceStone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Marks  []int32 `protobuf:"varint,1,rep,packed,name=marks,proto3" json:"marks,omitempty"` //标记id
	Lv     int32   `protobuf:"varint,2,opt,name=lv,proto3" json:"lv,omitempty"`              //等级
	Energy int32   `protobuf:"varint,3,opt,name=energy,proto3" json:"energy,omitempty"`      //剩余能量
}

func (x *SpaceStone) Reset() {
	*x = SpaceStone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpaceStone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpaceStone) ProtoMessage() {}

func (x *SpaceStone) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpaceStone.ProtoReflect.Descriptor instead.
func (*SpaceStone) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{82}
}

func (x *SpaceStone) GetMarks() []int32 {
	if x != nil {
		return x.Marks
	}
	return nil
}

func (x *SpaceStone) GetLv() int32 {
	if x != nil {
		return x.Lv
	}
	return 0
}

func (x *SpaceStone) GetEnergy() int32 {
	if x != nil {
		return x.Energy
	}
	return 0
}

// 玩家pvp模块数据
type PvpModuleData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ticket   map[int32]int32 `protobuf:"bytes,1,rep,name=ticket,proto3" json:"ticket,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`     //挑战次数数据
	Duration map[int32]int32 `protobuf:"bytes,2,rep,name=duration,proto3" json:"duration,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //持续时间数据
}

func (x *PvpModuleData) Reset() {
	*x = PvpModuleData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PvpModuleData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PvpModuleData) ProtoMessage() {}

func (x *PvpModuleData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PvpModuleData.ProtoReflect.Descriptor instead.
func (*PvpModuleData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{83}
}

func (x *PvpModuleData) GetTicket() map[int32]int32 {
	if x != nil {
		return x.Ticket
	}
	return nil
}

func (x *PvpModuleData) GetDuration() map[int32]int32 {
	if x != nil {
		return x.Duration
	}
	return nil
}

// 玩家普通pvp数据
type PvpNormalData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score       int32         `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`            //积分
	Rank        int32         `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`              //排名
	BattleRoles []*BattleRole `protobuf:"bytes,3,rep,name=battleRoles,proto3" json:"battleRoles,omitempty"` //阵容
}

func (x *PvpNormalData) Reset() {
	*x = PvpNormalData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PvpNormalData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PvpNormalData) ProtoMessage() {}

func (x *PvpNormalData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PvpNormalData.ProtoReflect.Descriptor instead.
func (*PvpNormalData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{84}
}

func (x *PvpNormalData) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *PvpNormalData) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *PvpNormalData) GetBattleRoles() []*BattleRole {
	if x != nil {
		return x.BattleRoles
	}
	return nil
}

// 玩家简单数据
type SimplePlayerData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`     //id
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` //名字
	Head string `protobuf:"bytes,3,opt,name=head,proto3" json:"head,omitempty"` //头像
}

func (x *SimplePlayerData) Reset() {
	*x = SimplePlayerData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimplePlayerData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimplePlayerData) ProtoMessage() {}

func (x *SimplePlayerData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimplePlayerData.ProtoReflect.Descriptor instead.
func (*SimplePlayerData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{85}
}

func (x *SimplePlayerData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SimplePlayerData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SimplePlayerData) GetHead() string {
	if x != nil {
		return x.Head
	}
	return ""
}

// 玩家简单pvp数据
type PvpSimplePlayerData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score       int32             `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`            //积分
	Rank        int32             `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`              //排名
	Ext         *SimplePlayerData `protobuf:"bytes,3,opt,name=ext,proto3" json:"ext,omitempty"`                 //简要数据
	BattleRoles []*BattleRole     `protobuf:"bytes,4,rep,name=battleRoles,proto3" json:"battleRoles,omitempty"` //阵容
}

func (x *PvpSimplePlayerData) Reset() {
	*x = PvpSimplePlayerData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PvpSimplePlayerData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PvpSimplePlayerData) ProtoMessage() {}

func (x *PvpSimplePlayerData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PvpSimplePlayerData.ProtoReflect.Descriptor instead.
func (*PvpSimplePlayerData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{86}
}

func (x *PvpSimplePlayerData) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *PvpSimplePlayerData) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *PvpSimplePlayerData) GetExt() *SimplePlayerData {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *PvpSimplePlayerData) GetBattleRoles() []*BattleRole {
	if x != nil {
		return x.BattleRoles
	}
	return nil
}

// pvp战绩数据
type PvpBattleRecordData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DocId       string               `protobuf:"bytes,1,opt,name=docId,proto3" json:"docId,omitempty"`              //文档id
	Attacker    *PvpSimplePlayerData `protobuf:"bytes,2,opt,name=attacker,proto3" json:"attacker,omitempty"`        //进攻方
	Defender    *PvpSimplePlayerData `protobuf:"bytes,3,opt,name=defender,proto3" json:"defender,omitempty"`        //防守方
	Result      int32                `protobuf:"varint,4,opt,name=result,proto3" json:"result,omitempty"`           //0平1胜2负
	Time        int32                `protobuf:"varint,5,opt,name=time,proto3" json:"time,omitempty"`               //多久之前发生的战斗
	ScoreChange int32                `protobuf:"varint,6,opt,name=scoreChange,proto3" json:"scoreChange,omitempty"` //积分变动
	Score1      int32                `protobuf:"varint,7,opt,name=score1,proto3" json:"score1,omitempty"`           //进攻方原始积分
	Score2      int32                `protobuf:"varint,8,opt,name=score2,proto3" json:"score2,omitempty"`           //防守方原始积分
}

func (x *PvpBattleRecordData) Reset() {
	*x = PvpBattleRecordData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PvpBattleRecordData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PvpBattleRecordData) ProtoMessage() {}

func (x *PvpBattleRecordData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PvpBattleRecordData.ProtoReflect.Descriptor instead.
func (*PvpBattleRecordData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{87}
}

func (x *PvpBattleRecordData) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

func (x *PvpBattleRecordData) GetAttacker() *PvpSimplePlayerData {
	if x != nil {
		return x.Attacker
	}
	return nil
}

func (x *PvpBattleRecordData) GetDefender() *PvpSimplePlayerData {
	if x != nil {
		return x.Defender
	}
	return nil
}

func (x *PvpBattleRecordData) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *PvpBattleRecordData) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *PvpBattleRecordData) GetScoreChange() int32 {
	if x != nil {
		return x.ScoreChange
	}
	return 0
}

func (x *PvpBattleRecordData) GetScore1() int32 {
	if x != nil {
		return x.Score1
	}
	return 0
}

func (x *PvpBattleRecordData) GetScore2() int32 {
	if x != nil {
		return x.Score2
	}
	return 0
}

// 探索队伍数据
type ExploreTeam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId    int32        `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"`       //星球ID
	Roles       []int32      `protobuf:"varint,2,rep,packed,name=roles,proto3" json:"roles,omitempty"`      //探索的乘客ID列表
	SurplusTime int32        `protobuf:"varint,3,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"` //剩余时间
	Rewards     []*Condition `protobuf:"bytes,4,rep,name=rewards,proto3" json:"rewards,omitempty"`          //探索奖励
}

func (x *ExploreTeam) Reset() {
	*x = ExploreTeam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExploreTeam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExploreTeam) ProtoMessage() {}

func (x *ExploreTeam) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExploreTeam.ProtoReflect.Descriptor instead.
func (*ExploreTeam) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{88}
}

func (x *ExploreTeam) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *ExploreTeam) GetRoles() []int32 {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *ExploreTeam) GetSurplusTime() int32 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

func (x *ExploreTeam) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

// 探索模块数据
type Explore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Teams []*ExploreTeam `protobuf:"bytes,1,rep,name=teams,proto3" json:"teams,omitempty"` //探索队伍列表
}

func (x *Explore) Reset() {
	*x = Explore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Explore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Explore) ProtoMessage() {}

func (x *Explore) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Explore.ProtoReflect.Descriptor instead.
func (*Explore) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{89}
}

func (x *Explore) GetTeams() []*ExploreTeam {
	if x != nil {
		return x.Teams
	}
	return nil
}

// 广告模块数据
type Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data map[int32]int32 `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //广告数据
}

func (x *Ad) Reset() {
	*x = Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ad) ProtoMessage() {}

func (x *Ad) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ad.ProtoReflect.Descriptor instead.
func (*Ad) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{90}
}

func (x *Ad) GetData() map[int32]int32 {
	if x != nil {
		return x.Data
	}
	return nil
}

// 记忆阁
type ProfileBranch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                   //当前关卡id
	Levels      []*ProfileBranchLevel `protobuf:"bytes,2,rep,name=levels,proto3" json:"levels,omitempty"`            //关卡列表
	Energy      int32                 `protobuf:"varint,3,opt,name=energy,proto3" json:"energy,omitempty"`           //体力
	SurplusTime int32                 `protobuf:"varint,4,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"` //体力剩余时间
}

func (x *ProfileBranch) Reset() {
	*x = ProfileBranch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProfileBranch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileBranch) ProtoMessage() {}

func (x *ProfileBranch) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileBranch.ProtoReflect.Descriptor instead.
func (*ProfileBranch) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{91}
}

func (x *ProfileBranch) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ProfileBranch) GetLevels() []*ProfileBranchLevel {
	if x != nil {
		return x.Levels
	}
	return nil
}

func (x *ProfileBranch) GetEnergy() int32 {
	if x != nil {
		return x.Energy
	}
	return 0
}

func (x *ProfileBranch) GetSurplusTime() int32 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

// 记忆阁关卡
type ProfileBranchLevel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int32              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`         //关卡id
	NodeId int32              `protobuf:"varint,2,opt,name=nodeId,proto3" json:"nodeId,omitempty"` //当前节点id
	Nodes  []*MapMineItemData `protobuf:"bytes,3,rep,name=nodes,proto3" json:"nodes,omitempty"`    //节点列表
	Unlock bool               `protobuf:"varint,4,opt,name=unlock,proto3" json:"unlock,omitempty"` //是否解锁
}

func (x *ProfileBranchLevel) Reset() {
	*x = ProfileBranchLevel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProfileBranchLevel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileBranchLevel) ProtoMessage() {}

func (x *ProfileBranchLevel) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileBranchLevel.ProtoReflect.Descriptor instead.
func (*ProfileBranchLevel) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{92}
}

func (x *ProfileBranchLevel) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ProfileBranchLevel) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *ProfileBranchLevel) GetNodes() []*MapMineItemData {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *ProfileBranchLevel) GetUnlock() bool {
	if x != nil {
		return x.Unlock
	}
	return false
}

// 列车任务
type TrainDailyTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*TrainDailyTaskItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"` //任务列表
}

func (x *TrainDailyTask) Reset() {
	*x = TrainDailyTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainDailyTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainDailyTask) ProtoMessage() {}

func (x *TrainDailyTask) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainDailyTask.ProtoReflect.Descriptor instead.
func (*TrainDailyTask) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{93}
}

func (x *TrainDailyTask) GetList() []*TrainDailyTaskItem {
	if x != nil {
		return x.List
	}
	return nil
}

// 列车任务
type TrainDailyTaskItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                              //任务id
	SurplusTime int32              `protobuf:"varint,2,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"`            //剩余时间
	State       CommonState        `protobuf:"varint,3,opt,name=state,proto3,enum=proto.CommonState" json:"state,omitempty"` //状态
	Rewards     []*Condition       `protobuf:"bytes,4,rep,name=rewards,proto3" json:"rewards,omitempty"`                     //奖励
	Conditions  []*WantedCondition `protobuf:"bytes,5,rep,name=conditions,proto3" json:"conditions,omitempty"`               //条件
	Roles       []int32            `protobuf:"varint,6,rep,packed,name=roles,proto3" json:"roles,omitempty"`                 //进行中的角色
	People      int32              `protobuf:"varint,7,opt,name=people,proto3" json:"people,omitempty"`                      //限定数量
	TrainId     int32              `protobuf:"varint,8,opt,name=trainId,proto3" json:"trainId,omitempty"`                    //限定车厢
	Level       int32              `protobuf:"varint,9,opt,name=level,proto3" json:"level,omitempty"`                        //星级
}

func (x *TrainDailyTaskItem) Reset() {
	*x = TrainDailyTaskItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainDailyTaskItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainDailyTaskItem) ProtoMessage() {}

func (x *TrainDailyTaskItem) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainDailyTaskItem.ProtoReflect.Descriptor instead.
func (*TrainDailyTaskItem) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{94}
}

func (x *TrainDailyTaskItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrainDailyTaskItem) GetSurplusTime() int32 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

func (x *TrainDailyTaskItem) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_NotStart
}

func (x *TrainDailyTaskItem) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *TrainDailyTaskItem) GetConditions() []*WantedCondition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *TrainDailyTaskItem) GetRoles() []int32 {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *TrainDailyTaskItem) GetPeople() int32 {
	if x != nil {
		return x.People
	}
	return 0
}

func (x *TrainDailyTaskItem) GetTrainId() int32 {
	if x != nil {
		return x.TrainId
	}
	return 0
}

func (x *TrainDailyTaskItem) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

// 突发任务
type BurstTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*BurstTaskItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"` //任务列表
}

func (x *BurstTask) Reset() {
	*x = BurstTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BurstTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BurstTask) ProtoMessage() {}

func (x *BurstTask) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BurstTask.ProtoReflect.Descriptor instead.
func (*BurstTask) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{95}
}

func (x *BurstTask) GetList() []*BurstTaskItem {
	if x != nil {
		return x.List
	}
	return nil
}

// 突发任务
type BurstTaskItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                              //任务id
	SurplusTime int32              `protobuf:"varint,2,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"`            //剩余时间
	State       CommonState        `protobuf:"varint,3,opt,name=state,proto3,enum=proto.CommonState" json:"state,omitempty"` //状态
	Rewards     []*Condition       `protobuf:"bytes,4,rep,name=rewards,proto3" json:"rewards,omitempty"`                     //奖励
	Conditions  []*WantedCondition `protobuf:"bytes,5,rep,name=conditions,proto3" json:"conditions,omitempty"`               //条件
	Roles       []int32            `protobuf:"varint,6,rep,packed,name=roles,proto3" json:"roles,omitempty"`                 //进行中的角色
	People      int32              `protobuf:"varint,7,opt,name=people,proto3" json:"people,omitempty"`                      //限定数量
	TrainId     int32              `protobuf:"varint,8,opt,name=trainId,proto3" json:"trainId,omitempty"`                    //限定车厢
}

func (x *BurstTaskItem) Reset() {
	*x = BurstTaskItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BurstTaskItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BurstTaskItem) ProtoMessage() {}

func (x *BurstTaskItem) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BurstTaskItem.ProtoReflect.Descriptor instead.
func (*BurstTaskItem) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{96}
}

func (x *BurstTaskItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BurstTaskItem) GetSurplusTime() int32 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

func (x *BurstTaskItem) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_NotStart
}

func (x *BurstTaskItem) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *BurstTaskItem) GetConditions() []*WantedCondition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *BurstTaskItem) GetRoles() []int32 {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *BurstTaskItem) GetPeople() int32 {
	if x != nil {
		return x.People
	}
	return 0
}

func (x *BurstTaskItem) GetTrainId() int32 {
	if x != nil {
		return x.TrainId
	}
	return 0
}

// 列车活动
type TrainActivity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List             []*TrainActivityItem         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`                          //任务列表
	ArrangeWorldTime int64                        `protobuf:"varint,2,opt,name=arrangeWorldTime,proto3" json:"arrangeWorldTime,omitempty"` //安排时的世界时间
	UngetRewards     []*TrainActivityUngetRewards `protobuf:"bytes,3,rep,name=ungetRewards,proto3" json:"ungetRewards,omitempty"`          //未领取奖励
}

func (x *TrainActivity) Reset() {
	*x = TrainActivity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainActivity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainActivity) ProtoMessage() {}

func (x *TrainActivity) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainActivity.ProtoReflect.Descriptor instead.
func (*TrainActivity) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{97}
}

func (x *TrainActivity) GetList() []*TrainActivityItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TrainActivity) GetArrangeWorldTime() int64 {
	if x != nil {
		return x.ArrangeWorldTime
	}
	return 0
}

func (x *TrainActivity) GetUngetRewards() []*TrainActivityUngetRewards {
	if x != nil {
		return x.UngetRewards
	}
	return nil
}

// 列车活动未领取奖励
type TrainActivityUngetRewards struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrainId int32        `protobuf:"varint,1,opt,name=trainId,proto3" json:"trainId,omitempty"` //车厢id
	Rewards []*Condition `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"`  //奖励
}

func (x *TrainActivityUngetRewards) Reset() {
	*x = TrainActivityUngetRewards{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainActivityUngetRewards) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainActivityUngetRewards) ProtoMessage() {}

func (x *TrainActivityUngetRewards) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainActivityUngetRewards.ProtoReflect.Descriptor instead.
func (*TrainActivityUngetRewards) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{98}
}

func (x *TrainActivityUngetRewards) GetTrainId() int32 {
	if x != nil {
		return x.TrainId
	}
	return 0
}

func (x *TrainActivityUngetRewards) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

// 列车活动数据
type TrainActivityItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                              //id
	CfgId       int32        `protobuf:"varint,2,opt,name=cfgId,proto3" json:"cfgId,omitempty"`                        //配置id
	TrainId     int32        `protobuf:"varint,3,opt,name=trainId,proto3" json:"trainId,omitempty"`                    //表现作用车厢id
	Rewards     []*Condition `protobuf:"bytes,4,rep,name=rewards,proto3" json:"rewards,omitempty"`                     //奖励
	SurplusTime int64        `protobuf:"varint,5,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"`            //剩余时间
	CostDay     int32        `protobuf:"varint,6,opt,name=costDay,proto3" json:"costDay,omitempty"`                    //消耗天数
	State       CommonState  `protobuf:"varint,7,opt,name=state,proto3,enum=proto.CommonState" json:"state,omitempty"` //状态
}

func (x *TrainActivityItem) Reset() {
	*x = TrainActivityItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainActivityItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainActivityItem) ProtoMessage() {}

func (x *TrainActivityItem) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainActivityItem.ProtoReflect.Descriptor instead.
func (*TrainActivityItem) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{99}
}

func (x *TrainActivityItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrainActivityItem) GetCfgId() int32 {
	if x != nil {
		return x.CfgId
	}
	return 0
}

func (x *TrainActivityItem) GetTrainId() int32 {
	if x != nil {
		return x.TrainId
	}
	return 0
}

func (x *TrainActivityItem) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *TrainActivityItem) GetSurplusTime() int64 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

func (x *TrainActivityItem) GetCostDay() int32 {
	if x != nil {
		return x.CostDay
	}
	return 0
}

func (x *TrainActivityItem) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_NotStart
}

// 科技数据
type TechData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data map[int32]int32 `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //科技数据
}

func (x *TechData) Reset() {
	*x = TechData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_struct_proto_msgTypes[100]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TechData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TechData) ProtoMessage() {}

func (x *TechData) ProtoReflect() protoreflect.Message {
	mi := &file_struct_proto_msgTypes[100]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TechData.ProtoReflect.Descriptor instead.
func (*TechData) Descriptor() ([]byte, []int) {
	return file_struct_proto_rawDescGZIP(), []int{100}
}

func (x *TechData) GetData() map[int32]int32 {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_struct_proto protoreflect.FileDescriptor

var file_struct_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xcd, 0x01, 0x0a, 0x0b, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x10, 0x0a,
	0x03, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x65, 0x72, 0x12,
	0x1e, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x47, 0x75, 0x69, 0x64, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x47, 0x75, 0x69, 0x64, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x53, 0x69, 0x67,
	0x6e, 0x22, 0xd6, 0x01, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x69,
	0x67, 0x6e, 0x4f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x73, 0x69, 0x67, 0x6e, 0x4f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x60, 0x0a, 0x08, 0x49, 0x74,
	0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75,
	0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xa9, 0x01, 0x0a,
	0x09, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x0a, 0x04, 0x68, 0x65,
	0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x68,
	0x65, 0x61, 0x64, 0x12, 0x31, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43,
	0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x61, 0x67, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x72,
	0x69, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x72, 0x69, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x61,
	0x74, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x77,
	0x61, 0x74, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x3e, 0x0a, 0x0e, 0x43, 0x61, 0x72, 0x72,
	0x69, 0x61, 0x67, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x12, 0x1a, 0x0a, 0x08,
	0x61, 0x63, 0x63, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x61, 0x63, 0x63, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x97, 0x04, 0x0a, 0x0c, 0x43, 0x61, 0x72,
	0x72, 0x69, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x68, 0x65,
	0x6d, 0x65, 0x4c, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x68, 0x65, 0x6d,
	0x65, 0x4c, 0x76, 0x12, 0x2c, 0x0a, 0x06, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x62, 0x75, 0x69, 0x6c, 0x64,
	0x73, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x44, 0x6f, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x44, 0x6f, 0x6f, 0x72, 0x12, 0x35, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x12, 0x37, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x72, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x0b,
	0x68, 0x65, 0x61, 0x72, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x3d, 0x0a, 0x0e, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x72, 0x69, 0x63, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x61, 0x72, 0x72,
	0x69, 0x61, 0x67, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x0e, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x72, 0x69, 0x63, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x37, 0x0a, 0x0b, 0x77, 0x61,
	0x74, 0x65, 0x72, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x0b, 0x77, 0x61, 0x74, 0x65, 0x72, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69,
	0x61, 0x67, 0x65, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x67, 0x6f,
	0x6f, 0x64, 0x73, 0x12, 0x3d, 0x0a, 0x0e, 0x76, 0x69, 0x74, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x4f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x52, 0x0e, 0x76, 0x69, 0x74, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x22, 0x49, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x76, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6c, 0x76, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6b, 0x69,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6b, 0x69, 0x6e, 0x22, 0x6f, 0x0a,
	0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x48, 0x69, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x69, 0x73, 0x48, 0x69, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x40,
	0x0a, 0x06, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x22, 0xd4, 0x01, 0x0a, 0x06, 0x45, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x65,
	0x6e, 0x65, 0x72, 0x67, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x65, 0x6e, 0x65,
	0x72, 0x67, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x6f, 0x73,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x66,
	0x72, 0x65, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x66, 0x72, 0x65, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x4e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x04, 0x75, 0x73, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x55, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x53, 0x70,
	0x65, 0x65, 0x64, 0x55, 0x70, 0x12, 0x30, 0x0a, 0x13, 0x69, 0x73, 0x55, 0x6e, 0x6c, 0x6f, 0x63,
	0x6b, 0x53, 0x70, 0x65, 0x65, 0x64, 0x55, 0x70, 0x41, 0x75, 0x74, 0x6f, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x13, 0x69, 0x73, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x70, 0x65, 0x65,
	0x64, 0x55, 0x70, 0x41, 0x75, 0x74, 0x6f, 0x22, 0x88, 0x04, 0x0a, 0x0d, 0x50, 0x61, 0x73, 0x73,
	0x65, 0x6e, 0x67, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x72, 0x4c, 0x76, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x72, 0x4c, 0x76, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x70, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x65, 0x78, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x72,
	0x6d, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x64, 0x6f, 0x72, 0x6d, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x72, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x72, 0x74, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x77,
	0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2a, 0x0a, 0x05, 0x70, 0x6c, 0x6f,
	0x74, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x50, 0x6c, 0x6f, 0x74, 0x52, 0x05,
	0x70, 0x6c, 0x6f, 0x74, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x53, 0x6b, 0x69, 0x6e,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x75, 0x73, 0x65,
	0x53, 0x6b, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x72,
	0x6d, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x64, 0x6f,
	0x72, 0x6d, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x30, 0x0a, 0x07, 0x74, 0x61, 0x6c, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x54, 0x61, 0x6c, 0x65, 0x6e, 0x74,
	0x52, 0x07, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3b, 0x0a, 0x07, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x1a, 0x3a, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x37, 0x0a, 0x0f, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x54,
	0x61, 0x6c, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x33, 0x0a, 0x0d, 0x50,
	0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x50, 0x6c, 0x6f, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x64, 0x6f, 0x6e, 0x65,
	0x22, 0xc3, 0x04, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x75, 0x72, 0x4d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63,
	0x75, 0x72, 0x4d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x75, 0x72, 0x4e, 0x6f,
	0x64, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x75, 0x72, 0x4e,
	0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x6e, 0x6f, 0x64,
	0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x6e, 0x6f, 0x64,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x10, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x74, 0x68, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6c, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x72, 0x65, 0x61, 0x63, 0x68, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x72, 0x65, 0x61, 0x63, 0x68, 0x65, 0x64, 0x12, 0x2f, 0x0a, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x63,
	0x68, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x52, 0x08,
	0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x2e, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x32, 0x0a, 0x14, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x05, 0x52, 0x14, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x72, 0x6f, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x3a, 0x0a, 0x18, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x69, 0x74, 0x79, 0x55, 0x6e, 0x47, 0x65, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x69, 0x74, 0x79, 0x55, 0x6e, 0x47, 0x65, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x69, 0x74,
	0x79, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x13, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x3e, 0x0a, 0x10, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xd2, 0x01, 0x0a, 0x0a, 0x50, 0x6c, 0x61, 0x6e, 0x65,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x75, 0x72, 0x50, 0x6c, 0x61, 0x6e,
	0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x50,
	0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x6f, 0x76, 0x65, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6d,
	0x6f, 0x76, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x6d,
	0x6f, 0x76, 0x65, 0x53, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6d, 0x6f, 0x76, 0x65, 0x53, 0x75, 0x72, 0x70, 0x6c, 0x75,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50,
	0x6c, 0x61, 0x6e, 0x65, 0x74, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x73, 0x12, 0x2b,
	0x0a, 0x08, 0x72, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x52, 0x08, 0x72, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x6e, 0x0a, 0x0c, 0x50,
	0x6c, 0x61, 0x6e, 0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d,
	0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b,
	0x6e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x30, 0x0a, 0x08, 0x52,
	0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x2e, 0x0a,
	0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x6c, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6c, 0x76, 0x22, 0x2e, 0x0a,
	0x0a, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e,
	0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x43, 0x0a,
	0x04, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x73, 0x22, 0x4b, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21,
	0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b,
	0x73, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x73, 0x22,
	0xd3, 0x01, 0x0a, 0x09, 0x54, 0x6f, 0x6f, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x4c, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x4c, 0x76, 0x12, 0x31, 0x0a,
	0x05, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54,
	0x6f, 0x6f, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x74, 0x6f, 0x6f, 0x6c, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x6c, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x6c, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x62, 0x6c, 0x65, 0x73, 0x73, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x62, 0x6c, 0x65, 0x73, 0x73, 0x49, 0x64, 0x1a, 0x49, 0x0a, 0x0a, 0x54, 0x6f,
	0x6f, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x25, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xff, 0x15, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x06,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x06, 0x52, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28,
	0x0a, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x06, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x61, 0x6d,
	0x6f, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x64, 0x69, 0x61, 0x6d, 0x6f,
	0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x68, 0x65, 0x61, 0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x68, 0x65, 0x61, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72,
	0x44, 0x75, 0x73, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x74, 0x61, 0x72,
	0x44, 0x75, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61, 0x70, 0x65, 0x72, 0x43, 0x72, 0x61,
	0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x61, 0x70, 0x65, 0x72, 0x43,
	0x72, 0x61, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x67, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x02, 0x67, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x06, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x06, 0x65, 0x6e, 0x65, 0x72,
	0x67, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x45, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x52, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x12,
	0x21, 0x0a, 0x03, 0x62, 0x61, 0x67, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x03, 0x62,
	0x61, 0x67, 0x12, 0x26, 0x0a, 0x05, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x05, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x12, 0x34, 0x0a, 0x0a, 0x70, 0x61,
	0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x73,
	0x12, 0x31, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6c, 0x61,
	0x6e, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x64, 0x65, 0x49, 0x64, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x67, 0x75, 0x69, 0x64, 0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a,
	0x09, 0x67, 0x75, 0x69, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x75, 0x69, 0x64, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x09, 0x67, 0x75, 0x69, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x0a,
	0x08, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x0a, 0x09, 0x74, 0x6f,
	0x6f, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x09, 0x74, 0x6f, 0x6f, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x07, 0x65, 0x78,
	0x70, 0x6c, 0x6f, 0x72, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x52, 0x07, 0x65, 0x78, 0x70,
	0x6c, 0x6f, 0x72, 0x65, 0x12, 0x25, 0x0a, 0x06, 0x77, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x57, 0x61, 0x6e,
	0x74, 0x65, 0x64, 0x52, 0x06, 0x77, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x6e,
	0x65, 0x78, 0x74, 0x44, 0x61, 0x79, 0x53, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6e, 0x65, 0x78, 0x74, 0x44, 0x61, 0x79, 0x53,
	0x75, 0x72, 0x70, 0x6c, 0x75, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x08, 0x6d, 0x61, 0x69,
	0x6c, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x6d, 0x61,
	0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x0f, 0x61, 0x63, 0x68, 0x69, 0x65, 0x76,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x63, 0x68, 0x69, 0x65, 0x76, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x61, 0x63, 0x68, 0x69, 0x65, 0x76, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6e, 0x74, 0x12, 0x34,
	0x0a, 0x0b, 0x6e, 0x65, 0x77, 0x4d, 0x61, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x1c, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4e, 0x65, 0x77, 0x4d,
	0x61, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x6e, 0x65, 0x77, 0x4d, 0x61, 0x72, 0x6b,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x05, 0x63, 0x68, 0x65, 0x73, 0x74, 0x18, 0x1d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x65, 0x73,
	0x74, 0x52, 0x05, 0x63, 0x68, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x72,
	0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x68,
	0x65, 0x61, 0x72, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x70, 0x61,
	0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x72, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x72, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x22, 0x0a, 0x05,
	0x74, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x05, 0x74, 0x6f, 0x77, 0x65, 0x72,
	0x12, 0x2e, 0x0a, 0x09, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x18, 0x21, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x61, 0x63,
	0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x52, 0x09, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65,
	0x12, 0x25, 0x0a, 0x06, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52,
	0x06, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x72, 0x65,
	0x73, 0x6f, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x05, 0x65, 0x71, 0x75, 0x69, 0x70,
	0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45,
	0x71, 0x75, 0x69, 0x70, 0x52, 0x05, 0x65, 0x71, 0x75, 0x69, 0x70, 0x12, 0x2b, 0x0a, 0x08, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x25, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x08,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x70, 0x61, 0x73, 0x73,
	0x65, 0x6e, 0x67, 0x65, 0x72, 0x52, 0x65, 0x73, 0x74, 0x43, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x26, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x74, 0x43, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x05, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x05, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x2b,
	0x0a, 0x04, 0x73, 0x6b, 0x69, 0x6e, 0x18, 0x28, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x2e, 0x53, 0x6b, 0x69, 0x6e,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x73, 0x6b, 0x69, 0x6e, 0x12, 0x28, 0x0a, 0x07, 0x6a,
	0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x18, 0x29, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x52, 0x07, 0x6a, 0x61,
	0x63, 0x6b, 0x70, 0x6f, 0x74, 0x12, 0x1c, 0x0a, 0x03, 0x70, 0x61, 0x79, 0x18, 0x2a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x79, 0x52, 0x03,
	0x70, 0x61, 0x79, 0x12, 0x2e, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x2b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x3a, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x64, 0x35,
	0x18, 0x2c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x64, 0x35, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x64, 0x35, 0x12,
	0x22, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x05, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x12, 0x2b, 0x0a, 0x04, 0x66, 0x72, 0x61, 0x67, 0x18, 0x2e, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x2e, 0x46, 0x72, 0x61, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x66, 0x72, 0x61, 0x67,
	0x12, 0x1c, 0x0a, 0x03, 0x6f, 0x72, 0x65, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x65, 0x52, 0x03, 0x6f, 0x72, 0x65, 0x12, 0x28,
	0x0a, 0x07, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x18, 0x30, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52,
	0x07, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x12, 0x2b, 0x0a, 0x06, 0x61, 0x72, 0x72, 0x65,
	0x73, 0x74, 0x18, 0x31, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x06, 0x61,
	0x72, 0x72, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x6e, 0x65, 0x78, 0x74, 0x57, 0x65, 0x65,
	0x6b, 0x53, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x32, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x13, 0x6e, 0x65, 0x78, 0x74, 0x57, 0x65, 0x65, 0x6b, 0x53, 0x75, 0x72, 0x70,
	0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x53, 0x74, 0x6f, 0x6e, 0x65, 0x18, 0x33, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x52, 0x0a,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x12, 0x32, 0x0a, 0x09, 0x64, 0x61,
	0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x18, 0x34, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2c,
	0x0a, 0x11, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x18, 0x35, 0x20, 0x03, 0x28, 0x05, 0x52, 0x11, 0x70, 0x61, 0x73, 0x73, 0x65,
	0x6e, 0x67, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0e,
	0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x36,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x37, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x11, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x38, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0d, 0x70, 0x76, 0x70, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x18, 0x39, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x50, 0x76, 0x70, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x0d, 0x70, 0x76, 0x70, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19,
	0x0a, 0x02, 0x61, 0x64, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x41, 0x64, 0x52, 0x02, 0x61, 0x64, 0x12, 0x3a, 0x0a, 0x0d, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x3d, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x54, 0x61, 0x73, 0x6b, 0x12, 0x2e, 0x0a, 0x09, 0x62, 0x75, 0x72, 0x73, 0x74, 0x54, 0x61, 0x73,
	0x6b, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x42, 0x75, 0x72, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x09, 0x62, 0x75, 0x72, 0x73, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x12, 0x3a, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x3e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x12, 0x2b, 0x0a, 0x08, 0x74, 0x65, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x18, 0x3f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x63, 0x68, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x08, 0x74, 0x65, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x51, 0x0a,
	0x09, 0x53, 0x6b, 0x69, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2e, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x53, 0x6b, 0x69,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x3c, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x64, 0x35, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x37,
	0x0a, 0x09, 0x46, 0x72, 0x61, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3d, 0x0a, 0x11, 0x50, 0x61, 0x73, 0x73, 0x65,
	0x6e, 0x67, 0x65, 0x72, 0x53, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x53, 0x6b, 0x69, 0x6e,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x25, 0x0a, 0x0d, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e,
	0x67, 0x65, 0x72, 0x53, 0x6b, 0x69, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x34, 0x0a,
	0x0c, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x76, 0x61, 0x6c, 0x22, 0x37, 0x0a, 0x09, 0x47, 0x75, 0x69, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x53, 0x74, 0x65, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x53, 0x74, 0x65, 0x70, 0x73, 0x22, 0xb6, 0x01, 0x0a,
	0x08, 0x4d, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x2a, 0x0a,
	0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x65, 0x61,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x72, 0x65, 0x61, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x22, 0x29, 0x0a, 0x07, 0x4d, 0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6c, 0x76,
	0x22, 0x2f, 0x0a, 0x06, 0x57, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x57, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0xd8, 0x02, 0x0a, 0x0a, 0x57, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x65,
	0x6f, 0x70, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x65, 0x6f, 0x70,
	0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x36, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x57, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x72,
	0x6f, 0x6c, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65,
	0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x62, 0x67, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x62, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x67, 0x41, 0x72, 0x67, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x62, 0x67, 0x41, 0x72, 0x67, 0x22, 0x3b, 0x0a, 0x0f,
	0x57, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x76, 0x0a, 0x0f, 0x41, 0x63, 0x68,
	0x69, 0x65, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x05,
	0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x12,
	0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x73, 0x12, 0x22, 0x0a,
	0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x06, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0x3d, 0x0a, 0x0b, 0x4e, 0x65, 0x77, 0x4d, 0x61, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x16, 0x0a, 0x06, 0x74, 0x79, 0x70, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x74, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x72, 0x79, 0x56,
	0x61, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x06, 0x61, 0x72, 0x79, 0x56, 0x61, 0x6c,
	0x22, 0x2d, 0x0a, 0x07, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x4c, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x4c, 0x76, 0x22,
	0x2b, 0x0a, 0x07, 0x42, 0x6f, 0x78, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x32, 0x0a, 0x0c,
	0x42, 0x6f, 0x78, 0x49, 0x6e, 0x66, 0x6f, 0x41, 0x72, 0x72, 0x61, 0x79, 0x12, 0x22, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x42, 0x6f, 0x78, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0xab, 0x01, 0x0a, 0x05, 0x43, 0x68, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65,
	0x64, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x61, 0x6c,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x73, 0x74, 0x65, 0x70, 0x12, 0x2a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x1a, 0x4c, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x29, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6f, 0x78, 0x49, 0x6e, 0x66, 0x6f, 0x41, 0x72,
	0x72, 0x61, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x43,
	0x0a, 0x05, 0x54, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x69,
	0x73, 0x44, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x44,
	0x6f, 0x6e, 0x65, 0x22, 0xd7, 0x03, 0x0a, 0x09, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x75, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x63, 0x75, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x65, 0x78, 0x74, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x65, 0x78, 0x74, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x03, 0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x4e, 0x6f,
	0x64, 0x65, 0x52, 0x03, 0x6d, 0x61, 0x70, 0x12, 0x2a, 0x0a, 0x05, 0x62, 0x75, 0x66, 0x66, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42,
	0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x42, 0x75, 0x66, 0x66, 0x52, 0x05, 0x62, 0x75,
	0x66, 0x66, 0x73, 0x12, 0x27, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x04,
	0x61, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x04, 0x61,
	0x69, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x61, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x64, 0x65, 0x61, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x04, 0x74, 0x65, 0x61,
	0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x04, 0x74, 0x65, 0x61, 0x6d,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x2c, 0x0a, 0x06, 0x62, 0x6f, 0x73, 0x73, 0x65, 0x73,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42,
	0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x42, 0x6f, 0x73, 0x73, 0x52, 0x06, 0x62, 0x6f,
	0x73, 0x73, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x06, 0x65, 0x71, 0x75, 0x69, 0x70, 0x73, 0x18, 0x0b,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x61,
	0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x45, 0x71, 0x75, 0x69, 0x70, 0x52, 0x06, 0x65, 0x71, 0x75,
	0x69, 0x70, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x1a, 0x0a, 0x08, 0x69, 0x73, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x69, 0x73, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x61,
	0x64, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x61, 0x64, 0x64, 0x22, 0xa2, 0x02,
	0x0a, 0x0d, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x65, 0x6e, 0x65, 0x6d, 0x69, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x65, 0x6d, 0x69, 0x65, 0x73,
	0x12, 0x2a, 0x0a, 0x05, 0x62, 0x75, 0x66, 0x66, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c,
	0x65, 0x42, 0x75, 0x66, 0x66, 0x52, 0x05, 0x62, 0x75, 0x66, 0x66, 0x73, 0x12, 0x25, 0x0a, 0x04,
	0x61, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x04, 0x61,
	0x69, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x73, 0x46, 0x6f, 0x67, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x46, 0x6f, 0x67, 0x12, 0x28, 0x0a, 0x06, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x65, 0x71, 0x75, 0x69, 0x70, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x61, 0x63,
	0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x45, 0x71, 0x75, 0x69, 0x70, 0x52, 0x06, 0x65, 0x71, 0x75, 0x69,
	0x70, 0x73, 0x22, 0xbc, 0x01, 0x0a, 0x0d, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65,
	0x42, 0x75, 0x66, 0x66, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x03, 0x61, 0x64, 0x64, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6c,
	0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x42, 0x75, 0x66, 0x66, 0x2e, 0x41, 0x64, 0x64, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x03, 0x61, 0x64, 0x64, 0x1a, 0x36, 0x0a, 0x08, 0x41, 0x64, 0x64,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x4e, 0x0a, 0x0e, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x22, 0x4e, 0x0a, 0x0d, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x42, 0x6f,
	0x73, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x27, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65,
	0x73, 0x22, 0x8a, 0x02, 0x0a, 0x0a, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x76, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x6c, 0x76, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x72, 0x4c, 0x76,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x72, 0x4c, 0x76, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x68, 0x70, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x68, 0x70, 0x12, 0x30, 0x0a, 0x07, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x54, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x52,
	0x07, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x28, 0x0a, 0x06, 0x65, 0x71, 0x75, 0x69,
	0x70, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x06, 0x65, 0x71, 0x75, 0x69,
	0x70, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x74, 0x74, 0x72, 0x52, 0x61, 0x74, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x61, 0x74, 0x74, 0x72, 0x52, 0x61, 0x74, 0x65, 0x22, 0x38,
	0x0a, 0x0c, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x73, 0x57, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69,
	0x73, 0x57, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x04, 0x75, 0x69, 0x64, 0x73, 0x22, 0x30, 0x0a, 0x0a, 0x42, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x75, 0x69, 0x64, 0x73, 0x22, 0x31, 0x0a, 0x06, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x22, 0xae, 0x01,
	0x0a, 0x05, 0x45, 0x71, 0x75, 0x69, 0x70, 0x12, 0x24, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a,
	0x0b, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70,
	0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x1a, 0x3e,
	0x0a, 0x10, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x47,
	0x0a, 0x0b, 0x45, 0x71, 0x75, 0x69, 0x70, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x61, 0x74, 0x74, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x61, 0x74, 0x74,
	0x72, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x85, 0x01, 0x0a, 0x09, 0x45, 0x71, 0x75, 0x69,
	0x70, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x2c, 0x0a,
	0x07, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x45, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x52, 0x07, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x73, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x75, 0x73, 0x65, 0x64, 0x22,
	0x68, 0x0a, 0x08, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x2a, 0x0a,
	0x10, 0x69, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x75, 0x7a, 0x7a, 0x6c,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x75, 0x7a, 0x7a, 0x6c, 0x65, 0x22, 0x2d, 0x0a, 0x05, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x85, 0x01, 0x0a, 0x09, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x05, 0x67, 0x6f,
	0x6f, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x20,
	0x0a, 0x0b, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0x85, 0x01, 0x0a, 0x05, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x12, 0x24, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x24, 0x0a, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x22, 0x87, 0x01, 0x0a, 0x07, 0x4a, 0x61, 0x63,
	0x6b, 0x70, 0x6f, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x6a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x44,
	0x61, 0x69, 0x6c, 0x79, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6a,
	0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4e, 0x75, 0x6d, 0x12, 0x2c,
	0x0a, 0x11, 0x6a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6a, 0x61, 0x63, 0x6b, 0x70,
	0x6f, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0d,
	0x6a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x6a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x50, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x22, 0xc8, 0x01, 0x0a, 0x03, 0x50, 0x61, 0x79, 0x12, 0x42, 0x0a, 0x0f, 0x6e, 0x6f,
	0x74, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4e, 0x6f, 0x74, 0x46,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x50, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x0f, 0x6e,
	0x6f, 0x74, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x3d,
	0x0a, 0x0b, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x79, 0x2e,
	0x50, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0b, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x1a, 0x3e, 0x0a,
	0x10, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x87, 0x01,
	0x0a, 0x11, 0x4e, 0x6f, 0x74, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x50, 0x61, 0x79, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x70, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x70, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0x33, 0x0a, 0x11, 0x43, 0x61, 0x72, 0x72, 0x69,
	0x61, 0x67, 0x65, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x6c, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6c, 0x76, 0x22, 0x37, 0x0a, 0x09,
	0x52, 0x65, 0x73, 0x6f, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x6c, 0x6f,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x52, 0x05,
	0x73, 0x6c, 0x6f, 0x74, 0x73, 0x22, 0x2f, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x6f, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x63, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x63, 0x64, 0x22, 0x47, 0x0a, 0x09, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x65, 0x78, 0x70, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0x8e, 0x03, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x72, 0x4c, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x72, 0x4c, 0x76, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x65, 0x6e,
	0x64, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x30, 0x0a,
	0x0a, 0x66, 0x69, 0x78, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x66, 0x69, 0x78, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12,
	0x2f, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x04, 0x72, 0x61, 0x72, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65,
	0x53, 0x74, 0x6f, 0x6e, 0x65, 0x4b, 0x65, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x3a, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x22, 0x5c, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x08, 0x6d, 0x6f, 0x6e, 0x73, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x08, 0x6d, 0x6f,
	0x6e, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x22, 0xf2,
	0x01, 0x0a, 0x05, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x2c, 0x0a, 0x08, 0x63, 0x65, 0x69, 0x6c,
	0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x65, 0x69, 0x6c, 0x52, 0x08, 0x63, 0x65,
	0x69, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2c, 0x0a, 0x08, 0x73, 0x65, 0x65, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x73, 0x65, 0x65, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x39, 0x0a, 0x09, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x2e, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x43, 0x6f, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x43, 0x6f, 0x6e, 0x64, 0x1a, 0x3c, 0x0a, 0x0e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x43, 0x6f,
	0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xdf, 0x01, 0x0a, 0x03, 0x4f, 0x72, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x72,
	0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a,
	0x08, 0x6f, 0x72, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x65, 0x2e, 0x4f, 0x72, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x65, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08,
	0x69, 0x73, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x69, 0x73, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x1a, 0x3b, 0x0a, 0x0d, 0x4f, 0x72, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x87, 0x01, 0x0a, 0x0c, 0x4f, 0x72, 0x65, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05,
	0x64, 0x65, 0x70, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x64, 0x65, 0x70,
	0x74, 0x68, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x65, 0x52, 0x6f, 0x77, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x73, 0x53,
	0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x72, 0x65, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x69, 0x73, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x72, 0x65, 0x61, 0x22,
	0x34, 0x0a, 0x0a, 0x4f, 0x72, 0x65, 0x52, 0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x65, 0x43, 0x65, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa7, 0x02, 0x0a, 0x0b, 0x4f, 0x72, 0x65, 0x43, 0x65, 0x69,
	0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x65, 0x43,
	0x65, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a,
	0x08, 0x6f, 0x72, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x08, 0x6f, 0x72, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x2d, 0x0a, 0x08, 0x6d,
	0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x52, 0x08, 0x6d, 0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1e, 0x0a, 0x03, 0x72, 0x65,
	0x66, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x03, 0x72, 0x65, 0x66, 0x12, 0x32, 0x0a, 0x08, 0x6e, 0x65,
	0x78, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x65, 0x50, 0x61, 0x67, 0x65, 0x4e, 0x65, 0x78, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x6e, 0x65, 0x78, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f,
	0x0a, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x65, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x72, 0x69, 0x6c, 0x6c, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x98, 0x01, 0x0a, 0x09, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x65, 0x69, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x43, 0x65, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x23, 0x0a, 0x05, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01,
	0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x79, 0x22,
	0x35, 0x0a, 0x07, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x12, 0x2a, 0x0a, 0x04, 0x6d, 0x69,
	0x6e, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x61, 0x70, 0x4d, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x6d, 0x69, 0x6e, 0x65, 0x22, 0x4f, 0x0a, 0x0d, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54,
	0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44,
	0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x62, 0x69, 0x67, 0x47, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x62, 0x69, 0x67, 0x47, 0x65, 0x74, 0x22, 0xc2, 0x02, 0x0a, 0x09, 0x44, 0x61, 0x69, 0x6c,
	0x79, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x12, 0x2c, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x28, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x62, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x52, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xb1, 0x01, 0x0a,
	0x0f, 0x4d, 0x61, 0x70, 0x4d, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x28,
	0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x08,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x61, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x73, 0x63, 0x61, 0x6c, 0x65,
	0x22, 0x47, 0x0a, 0x0f, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x12, 0x34, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x5d, 0x0a, 0x13, 0x54, 0x69, 0x6d,
	0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x72, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x61, 0x72, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x73, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x03, 0x75, 0x73, 0x65, 0x22, 0x96, 0x01, 0x0a, 0x0c, 0x41, 0x72, 0x72,
	0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x07, 0x61, 0x72, 0x72,
	0x65, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x52, 0x07, 0x61, 0x72, 0x72, 0x65, 0x73,
	0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2b, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x22, 0xb8, 0x02, 0x0a, 0x06, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x73, 0x74, 0x61, 0x72, 0x12, 0x28, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x72, 0x72, 0x65,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2d,
	0x0a, 0x08, 0x6d, 0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x52, 0x08, 0x6d, 0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12, 0x2a, 0x0a,
	0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x72, 0x72, 0x65, 0x73,
	0x74, 0x43, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xda, 0x01, 0x0a,
	0x0a, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x48, 0x69, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x48, 0x69, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x34, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x72, 0x72, 0x65,
	0x73, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x08, 0x74, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x84, 0x01, 0x0a, 0x0c, 0x41, 0x72,
	0x72, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2d, 0x0a, 0x04, 0x77, 0x69,
	0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x04, 0x77, 0x69, 0x6e, 0x73, 0x12, 0x2f, 0x0a, 0x05, 0x66, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x05, 0x66, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x22, 0x3e, 0x0a, 0x12, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x61, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x4a, 0x0a, 0x0a, 0x53, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x6d,
	0x61, 0x72, 0x6b, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x6c, 0x76, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x22, 0x81, 0x02, 0x0a,
	0x0d, 0x50, 0x76, 0x70, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x38,
	0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x76, 0x70, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x3e, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x50, 0x76, 0x70, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x39, 0x0a, 0x0b, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x3b, 0x0a, 0x0d, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x6e, 0x0a, 0x0d, 0x50, 0x76, 0x70, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x33, 0x0a, 0x0b, 0x62,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x52, 0x0b, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x73,
	0x22, 0x4a, 0x0a, 0x10, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x65, 0x61, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x65, 0x61, 0x64, 0x22, 0x9f, 0x01, 0x0a,
	0x13, 0x50, 0x76, 0x70, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61,
	0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x29,
	0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x33, 0x0a, 0x0b, 0x62, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x0b, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0x99,
	0x02, 0x0a, 0x13, 0x50, 0x76, 0x70, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x08,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x76, 0x70, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x61, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x08, 0x64, 0x65, 0x66, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50,
	0x76, 0x70, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x08, 0x64, 0x65, 0x66, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x31, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x31, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x32, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x32, 0x22, 0x8d, 0x01, 0x0a, 0x0b, 0x45,
	0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c,
	0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b,
	0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a,
	0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x33, 0x0a, 0x07, 0x45, 0x78,
	0x70, 0x6c, 0x6f, 0x72, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x78, 0x70,
	0x6c, 0x6f, 0x72, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x22,
	0x66, 0x0a, 0x02, 0x41, 0x64, 0x12, 0x27, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x64, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x37,
	0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8c, 0x01, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x31, 0x0a, 0x06, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x65, 0x6e,
	0x65, 0x72, 0x67, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c,
	0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x12, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e,
	0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x70,
	0x4d, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x6e, 0x6f,
	0x64, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x3f, 0x0a, 0x0e, 0x54,
	0x72, 0x61, 0x69, 0x6e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2d, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61,
	0x73, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xb2, 0x02, 0x0a,
	0x12, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x36, 0x0a, 0x0a, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x57, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x65, 0x6f,
	0x70, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x65, 0x6f, 0x70, 0x6c,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x22, 0x35, 0x0a, 0x09, 0x42, 0x75, 0x72, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x28,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x75, 0x72, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x97, 0x02, 0x0a, 0x0d, 0x42, 0x75, 0x72,
	0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75,
	0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x12, 0x36, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x57,
	0x61, 0x6e, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f,
	0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x70, 0x65, 0x6f, 0x70, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x70, 0x65, 0x6f, 0x70, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x72, 0x61, 0x69,
	0x6e, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e,
	0x49, 0x64, 0x22, 0xaf, 0x01, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x12, 0x2c, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x61, 0x72, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x57, 0x6f, 0x72,
	0x6c, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x61, 0x72,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x44,
	0x0a, 0x0c, 0x75, 0x6e, 0x67, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x55, 0x6e, 0x67, 0x65, 0x74, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x52, 0x0c, 0x75, 0x6e, 0x67, 0x65, 0x74, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x22, 0x61, 0x0a, 0x19, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x55, 0x6e, 0x67, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x07, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0xe5, 0x01, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x66, 0x67, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x66,
	0x67, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a,
	0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75, 0x72,
	0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x73, 0x74, 0x44, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x6f,
	0x73, 0x74, 0x44, 0x61, 0x79, 0x12, 0x28, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22,
	0x72, 0x0a, 0x08, 0x54, 0x65, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x54, 0x65, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x37, 0x0a, 0x09, 0x44, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_struct_proto_rawDescOnce sync.Once
	file_struct_proto_rawDescData = file_struct_proto_rawDesc
)

func file_struct_proto_rawDescGZIP() []byte {
	file_struct_proto_rawDescOnce.Do(func() {
		file_struct_proto_rawDescData = protoimpl.X.CompressGZIP(file_struct_proto_rawDescData)
	})
	return file_struct_proto_rawDescData
}

var file_struct_proto_msgTypes = make([]protoimpl.MessageInfo, 117)
var file_struct_proto_goTypes = []interface{}{
	(*LoginCommon)(nil),               // 0: proto.LoginCommon
	(*UserInfo)(nil),                  // 1: proto.UserInfo
	(*ItemInfo)(nil),                  // 2: proto.ItemInfo
	(*TrainInfo)(nil),                 // 3: proto.TrainInfo
	(*CarriageOutput)(nil),            // 4: proto.CarriageOutput
	(*CarriageInfo)(nil),              // 5: proto.CarriageInfo
	(*TrainItemInfo)(nil),             // 6: proto.TrainItemInfo
	(*Condition)(nil),                 // 7: proto.Condition
	(*Output)(nil),                    // 8: proto.Output
	(*Energy)(nil),                    // 9: proto.Energy
	(*PassengerInfo)(nil),             // 10: proto.PassengerInfo
	(*PassengerTalent)(nil),           // 11: proto.PassengerTalent
	(*PassengerPlot)(nil),             // 12: proto.PassengerPlot
	(*Planet)(nil),                    // 13: proto.Planet
	(*PlanetInfo)(nil),                // 14: proto.PlanetInfo
	(*PlanetBranch)(nil),              // 15: proto.PlanetBranch
	(*RageMode)(nil),                  // 16: proto.RageMode
	(*ToolInfo)(nil),                  // 17: proto.ToolInfo
	(*TaskTarget)(nil),                // 18: proto.TaskTarget
	(*Task)(nil),                      // 19: proto.Task
	(*TaskInfo)(nil),                  // 20: proto.TaskInfo
	(*ToolModel)(nil),                 // 21: proto.ToolModel
	(*Player)(nil),                    // 22: proto.Player
	(*PassengerSkinData)(nil),         // 23: proto.PassengerSkinData
	(*PassengerSkin)(nil),             // 24: proto.PassengerSkin
	(*CurrencyInfo)(nil),              // 25: proto.CurrencyInfo
	(*GuideInfo)(nil),                 // 26: proto.GuideInfo
	(*MailInfo)(nil),                  // 27: proto.MailInfo
	(*Monster)(nil),                   // 28: proto.Monster
	(*Wanted)(nil),                    // 29: proto.Wanted
	(*WantedInfo)(nil),                // 30: proto.WantedInfo
	(*WantedCondition)(nil),           // 31: proto.WantedCondition
	(*AchievementInfo)(nil),           // 32: proto.AchievementInfo
	(*NewMarkInfo)(nil),               // 33: proto.NewMarkInfo
	(*Storage)(nil),                   // 34: proto.Storage
	(*BoxInfo)(nil),                   // 35: proto.BoxInfo
	(*BoxInfoArray)(nil),              // 36: proto.BoxInfoArray
	(*Chest)(nil),                     // 37: proto.Chest
	(*Tower)(nil),                     // 38: proto.Tower
	(*BlackHole)(nil),                 // 39: proto.BlackHole
	(*BlackHoleNode)(nil),             // 40: proto.BlackHoleNode
	(*BlackHoleBuff)(nil),             // 41: proto.BlackHoleBuff
	(*BlackHoleEquip)(nil),            // 42: proto.BlackHoleEquip
	(*BlackHoleBoss)(nil),             // 43: proto.BlackHoleBoss
	(*BattleRole)(nil),                // 44: proto.BattleRole
	(*BattleResult)(nil),              // 45: proto.BattleResult
	(*BattleTeam)(nil),                // 46: proto.BattleTeam
	(*Battle)(nil),                    // 47: proto.Battle
	(*Equip)(nil),                     // 48: proto.Equip
	(*EquipEffect)(nil),               // 49: proto.EquipEffect
	(*EquipItem)(nil),                 // 50: proto.EquipItem
	(*Instance)(nil),                  // 51: proto.Instance
	(*Store)(nil),                     // 52: proto.Store
	(*StoreInfo)(nil),                 // 53: proto.StoreInfo
	(*Goods)(nil),                     // 54: proto.Goods
	(*Jackpot)(nil),                   // 55: proto.Jackpot
	(*Pay)(nil),                       // 56: proto.Pay
	(*NotFinishPayOrder)(nil),         // 57: proto.NotFinishPayOrder
	(*CarriageGoodsInfo)(nil),         // 58: proto.CarriageGoodsInfo
	(*Resonance)(nil),                 // 59: proto.Resonance
	(*ResonanceSlot)(nil),             // 60: proto.ResonanceSlot
	(*Transport)(nil),                 // 61: proto.Transport
	(*TransportData)(nil),             // 62: proto.TransportData
	(*TransportBattleData)(nil),       // 63: proto.TransportBattleData
	(*Field)(nil),                     // 64: proto.Field
	(*Ore)(nil),                       // 65: proto.Ore
	(*OreLevelData)(nil),              // 66: proto.OreLevelData
	(*OreRowData)(nil),                // 67: proto.OreRowData
	(*OreCeilData)(nil),               // 68: proto.OreCeilData
	(*FieldCeil)(nil),                 // 69: proto.FieldCeil
	(*Point)(nil),                     // 70: proto.Point
	(*Collect)(nil),                   // 71: proto.Collect
	(*DailyTaskInfo)(nil),             // 72: proto.DailyTaskInfo
	(*DailyTask)(nil),                 // 73: proto.DailyTask
	(*MapMineItemData)(nil),           // 74: proto.MapMineItemData
	(*TimeStoneRecord)(nil),           // 75: proto.TimeStoneRecord
	(*TimeStoneRecordData)(nil),       // 76: proto.TimeStoneRecordData
	(*ArrestModule)(nil),              // 77: proto.ArrestModule
	(*Arrest)(nil),                    // 78: proto.Arrest
	(*ArrestClue)(nil),                // 79: proto.ArrestClue
	(*ArrestResult)(nil),              // 80: proto.ArrestResult
	(*ArrestResultDetail)(nil),        // 81: proto.ArrestResultDetail
	(*SpaceStone)(nil),                // 82: proto.SpaceStone
	(*PvpModuleData)(nil),             // 83: proto.PvpModuleData
	(*PvpNormalData)(nil),             // 84: proto.PvpNormalData
	(*SimplePlayerData)(nil),          // 85: proto.SimplePlayerData
	(*PvpSimplePlayerData)(nil),       // 86: proto.PvpSimplePlayerData
	(*PvpBattleRecordData)(nil),       // 87: proto.PvpBattleRecordData
	(*ExploreTeam)(nil),               // 88: proto.ExploreTeam
	(*Explore)(nil),                   // 89: proto.Explore
	(*Ad)(nil),                        // 90: proto.Ad
	(*ProfileBranch)(nil),             // 91: proto.ProfileBranch
	(*ProfileBranchLevel)(nil),        // 92: proto.ProfileBranchLevel
	(*TrainDailyTask)(nil),            // 93: proto.TrainDailyTask
	(*TrainDailyTaskItem)(nil),        // 94: proto.TrainDailyTaskItem
	(*BurstTask)(nil),                 // 95: proto.BurstTask
	(*BurstTaskItem)(nil),             // 96: proto.BurstTaskItem
	(*TrainActivity)(nil),             // 97: proto.TrainActivity
	(*TrainActivityUngetRewards)(nil), // 98: proto.TrainActivityUngetRewards
	(*TrainActivityItem)(nil),         // 99: proto.TrainActivityItem
	(*TechData)(nil),                  // 100: proto.TechData
	nil,                               // 101: proto.PassengerInfo.ProfileEntry
	nil,                               // 102: proto.Planet.ProfileDataEntry
	nil,                               // 103: proto.ToolModel.ToolsEntry
	nil,                               // 104: proto.Player.SkinEntry
	nil,                               // 105: proto.Player.ConfigMd5Entry
	nil,                               // 106: proto.Player.FragEntry
	nil,                               // 107: proto.Chest.DataEntry
	nil,                               // 108: proto.BlackHoleBuff.AddEntry
	nil,                               // 109: proto.Equip.ProficiencyEntry
	nil,                               // 110: proto.Pay.PayCountMapEntry
	nil,                               // 111: proto.Field.LevelCondEntry
	nil,                               // 112: proto.Ore.OreItemsEntry
	nil,                               // 113: proto.PvpModuleData.TicketEntry
	nil,                               // 114: proto.PvpModuleData.DurationEntry
	nil,                               // 115: proto.Ad.DataEntry
	nil,                               // 116: proto.TechData.DataEntry
	(TransportDataState)(0),           // 117: proto.TransportDataState
	(OreCeilType)(0),                  // 118: proto.OreCeilType
	(OrePageNextType)(0),              // 119: proto.OrePageNextType
	(OreBlockItemDrillDirection)(0),   // 120: proto.OreBlockItemDrillDirection
	(FieldCeilState)(0),               // 121: proto.FieldCeilState
	(ArrestState)(0),                  // 122: proto.ArrestState
	(ArrestClueType)(0),               // 123: proto.ArrestClueType
	(ArrestPlaceType)(0),              // 124: proto.ArrestPlaceType
	(ArrestTimeType)(0),               // 125: proto.ArrestTimeType
	(CommonState)(0),                  // 126: proto.CommonState
}
var file_struct_proto_depIdxs = []int32{
	5,   // 0: proto.TrainInfo.head:type_name -> proto.CarriageInfo
	5,   // 1: proto.TrainInfo.carriages:type_name -> proto.CarriageInfo
	6,   // 2: proto.CarriageInfo.builds:type_name -> proto.TrainItemInfo
	4,   // 3: proto.CarriageInfo.starOutput:type_name -> proto.CarriageOutput
	4,   // 4: proto.CarriageInfo.heartOutput:type_name -> proto.CarriageOutput
	4,   // 5: proto.CarriageInfo.electricOutput:type_name -> proto.CarriageOutput
	4,   // 6: proto.CarriageInfo.waterOutput:type_name -> proto.CarriageOutput
	58,  // 7: proto.CarriageInfo.goods:type_name -> proto.CarriageGoodsInfo
	4,   // 8: proto.CarriageInfo.vitalityOutput:type_name -> proto.CarriageOutput
	7,   // 9: proto.Output.items:type_name -> proto.Condition
	12,  // 10: proto.PassengerInfo.plots:type_name -> proto.PassengerPlot
	11,  // 11: proto.PassengerInfo.talents:type_name -> proto.PassengerTalent
	101, // 12: proto.PassengerInfo.profile:type_name -> proto.PassengerInfo.ProfileEntry
	15,  // 13: proto.Planet.branches:type_name -> proto.PlanetBranch
	102, // 14: proto.Planet.profileData:type_name -> proto.Planet.ProfileDataEntry
	13,  // 15: proto.PlanetInfo.planets:type_name -> proto.Planet
	16,  // 16: proto.PlanetInfo.rageMode:type_name -> proto.RageMode
	18,  // 17: proto.Task.targets:type_name -> proto.TaskTarget
	19,  // 18: proto.TaskInfo.tasks:type_name -> proto.Task
	103, // 19: proto.ToolModel.tools:type_name -> proto.ToolModel.ToolsEntry
	9,   // 20: proto.Player.energy:type_name -> proto.Energy
	2,   // 21: proto.Player.bag:type_name -> proto.ItemInfo
	3,   // 22: proto.Player.train:type_name -> proto.TrainInfo
	10,  // 23: proto.Player.passengers:type_name -> proto.PassengerInfo
	14,  // 24: proto.Player.planetInfo:type_name -> proto.PlanetInfo
	26,  // 25: proto.Player.guideInfo:type_name -> proto.GuideInfo
	20,  // 26: proto.Player.taskInfo:type_name -> proto.TaskInfo
	21,  // 27: proto.Player.toolModel:type_name -> proto.ToolModel
	89,  // 28: proto.Player.explore:type_name -> proto.Explore
	29,  // 29: proto.Player.wanted:type_name -> proto.Wanted
	27,  // 30: proto.Player.mailList:type_name -> proto.MailInfo
	32,  // 31: proto.Player.achievementInfo:type_name -> proto.AchievementInfo
	33,  // 32: proto.Player.newMarkList:type_name -> proto.NewMarkInfo
	37,  // 33: proto.Player.chest:type_name -> proto.Chest
	38,  // 34: proto.Player.tower:type_name -> proto.Tower
	39,  // 35: proto.Player.blackHole:type_name -> proto.BlackHole
	47,  // 36: proto.Player.battle:type_name -> proto.Battle
	59,  // 37: proto.Player.resonance:type_name -> proto.Resonance
	48,  // 38: proto.Player.equip:type_name -> proto.Equip
	51,  // 39: proto.Player.instance:type_name -> proto.Instance
	52,  // 40: proto.Player.store:type_name -> proto.Store
	104, // 41: proto.Player.skin:type_name -> proto.Player.SkinEntry
	55,  // 42: proto.Player.jackpot:type_name -> proto.Jackpot
	56,  // 43: proto.Player.pay:type_name -> proto.Pay
	61,  // 44: proto.Player.transport:type_name -> proto.Transport
	105, // 45: proto.Player.configMd5:type_name -> proto.Player.ConfigMd5Entry
	64,  // 46: proto.Player.field:type_name -> proto.Field
	106, // 47: proto.Player.frag:type_name -> proto.Player.FragEntry
	65,  // 48: proto.Player.ore:type_name -> proto.Ore
	71,  // 49: proto.Player.collect:type_name -> proto.Collect
	77,  // 50: proto.Player.arrest:type_name -> proto.ArrestModule
	82,  // 51: proto.Player.spaceStone:type_name -> proto.SpaceStone
	72,  // 52: proto.Player.dailyTask:type_name -> proto.DailyTaskInfo
	83,  // 53: proto.Player.pvpModuleData:type_name -> proto.PvpModuleData
	90,  // 54: proto.Player.ad:type_name -> proto.Ad
	91,  // 55: proto.Player.profileBranch:type_name -> proto.ProfileBranch
	93,  // 56: proto.Player.trainDailyTask:type_name -> proto.TrainDailyTask
	95,  // 57: proto.Player.burstTask:type_name -> proto.BurstTask
	97,  // 58: proto.Player.trainActivity:type_name -> proto.TrainActivity
	100, // 59: proto.Player.techData:type_name -> proto.TechData
	24,  // 60: proto.PassengerSkinData.list:type_name -> proto.PassengerSkin
	7,   // 61: proto.MailInfo.rewards:type_name -> proto.Condition
	30,  // 62: proto.Wanted.list:type_name -> proto.WantedInfo
	7,   // 63: proto.WantedInfo.rewards:type_name -> proto.Condition
	31,  // 64: proto.WantedInfo.conditions:type_name -> proto.WantedCondition
	19,  // 65: proto.AchievementInfo.tasks:type_name -> proto.Task
	35,  // 66: proto.BoxInfoArray.data:type_name -> proto.BoxInfo
	107, // 67: proto.Chest.data:type_name -> proto.Chest.DataEntry
	40,  // 68: proto.BlackHole.map:type_name -> proto.BlackHoleNode
	41,  // 69: proto.BlackHole.buffs:type_name -> proto.BlackHoleBuff
	44,  // 70: proto.BlackHole.roles:type_name -> proto.BattleRole
	44,  // 71: proto.BlackHole.aids:type_name -> proto.BattleRole
	46,  // 72: proto.BlackHole.team:type_name -> proto.BattleTeam
	43,  // 73: proto.BlackHole.bosses:type_name -> proto.BlackHoleBoss
	42,  // 74: proto.BlackHole.equips:type_name -> proto.BlackHoleEquip
	44,  // 75: proto.BlackHoleNode.enemies:type_name -> proto.BattleRole
	41,  // 76: proto.BlackHoleNode.buffs:type_name -> proto.BlackHoleBuff
	44,  // 77: proto.BlackHoleNode.aids:type_name -> proto.BattleRole
	7,   // 78: proto.BlackHoleNode.reward:type_name -> proto.Condition
	42,  // 79: proto.BlackHoleNode.equips:type_name -> proto.BlackHoleEquip
	108, // 80: proto.BlackHoleBuff.add:type_name -> proto.BlackHoleBuff.AddEntry
	44,  // 81: proto.BlackHoleBoss.roles:type_name -> proto.BattleRole
	11,  // 82: proto.BattleRole.talents:type_name -> proto.PassengerTalent
	50,  // 83: proto.BattleRole.equips:type_name -> proto.EquipItem
	46,  // 84: proto.Battle.teams:type_name -> proto.BattleTeam
	50,  // 85: proto.Equip.data:type_name -> proto.EquipItem
	109, // 86: proto.Equip.proficiency:type_name -> proto.Equip.ProficiencyEntry
	49,  // 87: proto.EquipItem.effects:type_name -> proto.EquipEffect
	53,  // 88: proto.Store.list:type_name -> proto.StoreInfo
	54,  // 89: proto.StoreInfo.goods:type_name -> proto.Goods
	7,   // 90: proto.Goods.item:type_name -> proto.Condition
	7,   // 91: proto.Goods.cost:type_name -> proto.Condition
	57,  // 92: proto.Pay.notFinishOrders:type_name -> proto.NotFinishPayOrder
	110, // 93: proto.Pay.payCountMap:type_name -> proto.Pay.PayCountMapEntry
	60,  // 94: proto.Resonance.slots:type_name -> proto.ResonanceSlot
	62,  // 95: proto.Transport.list:type_name -> proto.TransportData
	7,   // 96: proto.TransportData.rewards:type_name -> proto.Condition
	7,   // 97: proto.TransportData.fixRewards:type_name -> proto.Condition
	117, // 98: proto.TransportData.state:type_name -> proto.TransportDataState
	63,  // 99: proto.TransportData.battleData:type_name -> proto.TransportBattleData
	44,  // 100: proto.TransportBattleData.monsters:type_name -> proto.BattleRole
	69,  // 101: proto.Field.ceilData:type_name -> proto.FieldCeil
	7,   // 102: proto.Field.seedData:type_name -> proto.Condition
	111, // 103: proto.Field.levelCond:type_name -> proto.Field.LevelCondEntry
	112, // 104: proto.Ore.oreItems:type_name -> proto.Ore.OreItemsEntry
	66,  // 105: proto.Ore.data:type_name -> proto.OreLevelData
	67,  // 106: proto.OreLevelData.data:type_name -> proto.OreRowData
	68,  // 107: proto.OreRowData.data:type_name -> proto.OreCeilData
	118, // 108: proto.OreCeilData.type:type_name -> proto.OreCeilType
	7,   // 109: proto.OreCeilData.oreExtra:type_name -> proto.Condition
	44,  // 110: proto.OreCeilData.monsters:type_name -> proto.BattleRole
	70,  // 111: proto.OreCeilData.ref:type_name -> proto.Point
	119, // 112: proto.OreCeilData.nextType:type_name -> proto.OrePageNextType
	120, // 113: proto.OreCeilData.direction:type_name -> proto.OreBlockItemDrillDirection
	121, // 114: proto.FieldCeil.state:type_name -> proto.FieldCeilState
	74,  // 115: proto.Collect.mine:type_name -> proto.MapMineItemData
	73,  // 116: proto.DailyTaskInfo.tasks:type_name -> proto.DailyTask
	7,   // 117: proto.DailyTask.target:type_name -> proto.Condition
	7,   // 118: proto.DailyTask.progress:type_name -> proto.Condition
	7,   // 119: proto.DailyTask.reward:type_name -> proto.Condition
	44,  // 120: proto.DailyTask.battleInfo:type_name -> proto.BattleRole
	7,   // 121: proto.MapMineItemData.reward:type_name -> proto.Condition
	70,  // 122: proto.MapMineItemData.position:type_name -> proto.Point
	76,  // 123: proto.TimeStoneRecord.records:type_name -> proto.TimeStoneRecordData
	78,  // 124: proto.ArrestModule.arrests:type_name -> proto.Arrest
	80,  // 125: proto.ArrestModule.result:type_name -> proto.ArrestResult
	122, // 126: proto.Arrest.state:type_name -> proto.ArrestState
	44,  // 127: proto.Arrest.monsters:type_name -> proto.BattleRole
	7,   // 128: proto.Arrest.rewards:type_name -> proto.Condition
	79,  // 129: proto.Arrest.clues:type_name -> proto.ArrestClue
	123, // 130: proto.ArrestClue.type:type_name -> proto.ArrestClueType
	124, // 131: proto.ArrestClue.placeType:type_name -> proto.ArrestPlaceType
	125, // 132: proto.ArrestClue.timeType:type_name -> proto.ArrestTimeType
	81,  // 133: proto.ArrestResult.wins:type_name -> proto.ArrestResultDetail
	81,  // 134: proto.ArrestResult.fails:type_name -> proto.ArrestResultDetail
	113, // 135: proto.PvpModuleData.ticket:type_name -> proto.PvpModuleData.TicketEntry
	114, // 136: proto.PvpModuleData.duration:type_name -> proto.PvpModuleData.DurationEntry
	44,  // 137: proto.PvpNormalData.battleRoles:type_name -> proto.BattleRole
	85,  // 138: proto.PvpSimplePlayerData.ext:type_name -> proto.SimplePlayerData
	44,  // 139: proto.PvpSimplePlayerData.battleRoles:type_name -> proto.BattleRole
	86,  // 140: proto.PvpBattleRecordData.attacker:type_name -> proto.PvpSimplePlayerData
	86,  // 141: proto.PvpBattleRecordData.defender:type_name -> proto.PvpSimplePlayerData
	7,   // 142: proto.ExploreTeam.rewards:type_name -> proto.Condition
	88,  // 143: proto.Explore.teams:type_name -> proto.ExploreTeam
	115, // 144: proto.Ad.data:type_name -> proto.Ad.DataEntry
	92,  // 145: proto.ProfileBranch.levels:type_name -> proto.ProfileBranchLevel
	74,  // 146: proto.ProfileBranchLevel.nodes:type_name -> proto.MapMineItemData
	94,  // 147: proto.TrainDailyTask.list:type_name -> proto.TrainDailyTaskItem
	126, // 148: proto.TrainDailyTaskItem.state:type_name -> proto.CommonState
	7,   // 149: proto.TrainDailyTaskItem.rewards:type_name -> proto.Condition
	31,  // 150: proto.TrainDailyTaskItem.conditions:type_name -> proto.WantedCondition
	96,  // 151: proto.BurstTask.list:type_name -> proto.BurstTaskItem
	126, // 152: proto.BurstTaskItem.state:type_name -> proto.CommonState
	7,   // 153: proto.BurstTaskItem.rewards:type_name -> proto.Condition
	31,  // 154: proto.BurstTaskItem.conditions:type_name -> proto.WantedCondition
	99,  // 155: proto.TrainActivity.list:type_name -> proto.TrainActivityItem
	98,  // 156: proto.TrainActivity.ungetRewards:type_name -> proto.TrainActivityUngetRewards
	7,   // 157: proto.TrainActivityUngetRewards.rewards:type_name -> proto.Condition
	7,   // 158: proto.TrainActivityItem.rewards:type_name -> proto.Condition
	126, // 159: proto.TrainActivityItem.state:type_name -> proto.CommonState
	116, // 160: proto.TechData.data:type_name -> proto.TechData.DataEntry
	17,  // 161: proto.ToolModel.ToolsEntry.value:type_name -> proto.ToolInfo
	23,  // 162: proto.Player.SkinEntry.value:type_name -> proto.PassengerSkinData
	36,  // 163: proto.Chest.DataEntry.value:type_name -> proto.BoxInfoArray
	164, // [164:164] is the sub-list for method output_type
	164, // [164:164] is the sub-list for method input_type
	164, // [164:164] is the sub-list for extension type_name
	164, // [164:164] is the sub-list for extension extendee
	0,   // [0:164] is the sub-list for field type_name
}

func init() { file_struct_proto_init() }
func file_struct_proto_init() {
	if File_struct_proto != nil {
		return
	}
	file_enum_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_struct_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginCommon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrainInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarriageOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarriageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrainItemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Condition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Output); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Energy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PassengerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PassengerTalent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PassengerPlot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Planet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlanetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlanetBranch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RageMode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ToolInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskTarget); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ToolModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PassengerSkinData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PassengerSkin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrencyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GuideInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MailInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Monster); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Wanted); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WantedInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WantedCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AchievementInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewMarkInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Storage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoxInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoxInfoArray); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Chest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Tower); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlackHole); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlackHoleNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlackHoleBuff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlackHoleEquip); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlackHoleBoss); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BattleRole); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BattleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BattleTeam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Battle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Equip); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipEffect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Instance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Store); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Goods); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Jackpot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotFinishPayOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarriageGoodsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Resonance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResonanceSlot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Transport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransportData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransportBattleData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Field); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OreLevelData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OreRowData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OreCeilData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldCeil); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Point); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Collect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyTaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MapMineItemData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeStoneRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeStoneRecordData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArrestModule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Arrest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArrestClue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArrestResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArrestResultDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpaceStone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PvpModuleData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PvpNormalData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimplePlayerData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PvpSimplePlayerData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PvpBattleRecordData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExploreTeam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Explore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProfileBranch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProfileBranchLevel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrainDailyTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrainDailyTaskItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BurstTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BurstTaskItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrainActivity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[98].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrainActivityUngetRewards); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrainActivityItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_struct_proto_msgTypes[100].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TechData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_struct_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   117,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_struct_proto_goTypes,
		DependencyIndexes: file_struct_proto_depIdxs,
		MessageInfos:      file_struct_proto_msgTypes,
	}.Build()
	File_struct_proto = out.File
	file_struct_proto_rawDesc = nil
	file_struct_proto_goTypes = nil
	file_struct_proto_depIdxs = nil
}
